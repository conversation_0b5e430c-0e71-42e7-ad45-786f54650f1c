interface ModelConfig {
  model_id: string;
  top_p: number;
  top_k: number;
  temperature: number;
  max_token: number;
  provider: string;
}

interface Reconnect {
  enable: boolean;
  delayMs: number;
  maxAttempts: number;
}

interface McpConfig {
  mcpNameServer: string;
  mcpPort: number;
  url: string;
  useNodeEventSource: boolean;
  header: Record<string, string>;
  reconnect: Reconnect;
}

export interface Agent {
  user_id: number;
  id: string;
  name?: string;
  description?: string;
  model: ModelConfig;
  instruction: string;
  mcp_config?: McpConfig;
  children?: Agent[]
}
