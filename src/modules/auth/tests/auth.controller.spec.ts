import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from '../controller/auth.controller';
import { AuthService } from '../service/auth.service';
import { LoginDto, RegisterDto, VerifyOtpDto } from '../dto';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { VerifyForgotPasswordDto } from '../dto/verify-forgot-password.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { Response } from 'express';
// Mock ApiResponse instead of importing it
class ApiResponse<T> {
  code: number;
  message: string;
  result?: T;
}
import { LoginResponse } from '../dto/login.dto';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  // Mock response object
  const mockResponse = {
    cookie: jest.fn().mockReturnThis(),
  } as unknown as Response;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            login: jest.fn(),
            register: jest.fn(),
            verifyOtp: jest.fn(),
            forgotPassword: jest.fn(),
            verifyForgotPassword: jest.fn(),
            resetPassword: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('login', () => {
    it('should return login result and set refresh token cookie', async () => {
      // Arrange
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
        recaptchaToken: 'recaptcha-token',
      };

      const mockLoginResult = {
        result: {
          code: 200,
          message: 'Đăng nhập thành công',
          result: {
            accessToken: 'access-token',
            expiresIn: 86400,
            info: [],
            user: {
              id: 1,
              email: '<EMAIL>',
              username: '',
              permissions: ['read:profile'],
              status: 'active',
            },
          },
        },
        refreshToken: 'refresh-token',
      };

      jest.spyOn(authService, 'login').mockResolvedValue(mockLoginResult);

      // Act
      const result = await controller.login(loginDto, mockResponse);

      // Assert
      expect(authService.login).toHaveBeenCalledWith(loginDto);
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        'refresh_token',
        'refresh-token',
        expect.objectContaining({
          httpOnly: true,
          secure: true,
          sameSite: 'strict',
          path: '/',
        }),
      );
      expect(result).toEqual(mockLoginResult.result);
    });

    it('should handle verification required response', async () => {
      // Arrange
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
        recaptchaToken: 'recaptcha-token',
      };

      const mockLoginResult = {
        result: {
          code: 202,
          message: 'Vui lòng xác thực email hoặc số điện thoại',
          result: {
            verifyToken: 'verify-token',
            info: [
              {
                platform: 'EMAIL',
                value: '<EMAIL>',
              },
            ],
            expiresIn: 300,
          },
        },
        refreshToken: '',
      };

      jest.spyOn(authService, 'login').mockResolvedValue(mockLoginResult);

      // Act
      const result = await controller.login(loginDto, mockResponse);

      // Assert
      expect(authService.login).toHaveBeenCalledWith(loginDto);
      expect(result).toEqual(mockLoginResult.result);
      // Cookie should not be set when verification is required
      expect(mockResponse.cookie).not.toHaveBeenCalled();
    });
  });

  describe('register', () => {
    it('should register a new user and return OTP information', async () => {
      // Arrange
      const registerDto: RegisterDto = {
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '0912345678',
      };

      const mockRegisterResult = {
        code: 200,
        message: 'Mã OTP đã được gửi đến email và số điện thoại của bạn',
        result: {
          otpToken: 'otp-token',
          expiresIn: 300,
          maskedEmail: 't***@example.com',
          maskedPhoneNumber: '091****678',
        },
      };

      jest.spyOn(authService, 'register').mockResolvedValue(mockRegisterResult);

      // Act
      const result = await controller.register(registerDto);

      // Assert
      expect(authService.register).toHaveBeenCalledWith(registerDto);
      expect(result).toEqual(mockRegisterResult);
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP, complete registration and set refresh token cookie', async () => {
      // Arrange
      const verifyOtpDto: VerifyOtpDto = {
        otp: '123456',
        otpToken: 'otp-token',
      };

      const mockVerifyResult = {
        result: {
          accessToken: 'access-token',
          user: {
            id: 1,
            email: '<EMAIL>',
            fullName: 'Test User',
            role: 'user',
          },
        },
        refreshToken: 'refresh-token',
      };

      jest.spyOn(authService, 'verifyOtp').mockResolvedValue(mockVerifyResult);

      // Act
      const result = await controller.verifyOtp(verifyOtpDto, mockResponse);

      // Assert
      expect(authService.verifyOtp).toHaveBeenCalledWith(verifyOtpDto);
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        'refresh_token',
        'refresh-token',
        expect.objectContaining({
          httpOnly: true,
          secure: true,
          sameSite: 'strict',
          path: '/',
        }),
      );
      expect(result).toEqual(mockVerifyResult.result);
    });
  });

  describe('forgotPassword', () => {
    it('should initiate forgot password process and return OTP information', async () => {
      // Arrange
      const forgotPasswordDto: ForgotPasswordDto = {
        email: '<EMAIL>',
      };

      const mockForgotPasswordResult = {
        code: 200,
        message: 'Mã OTP đã được gửi đến email của bạn',
        result: {
          otpToken: 'otp-token',
          expiresIn: 300,
          maskedEmail: 't***@example.com',
        },
      };

      jest.spyOn(authService, 'forgotPassword').mockResolvedValue(mockForgotPasswordResult);

      // Act
      const result = await controller.forgotPassword(forgotPasswordDto);

      // Assert
      expect(authService.forgotPassword).toHaveBeenCalledWith(forgotPasswordDto);
      expect(result).toEqual(mockForgotPasswordResult);
    });
  });

  describe('verifyForgotPassword', () => {
    it('should verify forgot password OTP and return change password token', async () => {
      // Arrange
      const verifyForgotPasswordDto: VerifyForgotPasswordDto = {
        otp: '123456',
        otpToken: 'otp-token',
      };

      const mockVerifyResult = {
        code: 200,
        message: 'Xác thực OTP thành công',
        result: {
          changePasswordToken: 'change-password-token',
          expiresIn: 900,
        },
      };

      jest.spyOn(authService, 'verifyForgotPassword').mockResolvedValue(mockVerifyResult);

      // Act
      const result = await controller.verifyForgotPassword(verifyForgotPasswordDto);

      // Assert
      expect(authService.verifyForgotPassword).toHaveBeenCalledWith(verifyForgotPasswordDto);
      expect(result).toEqual(mockVerifyResult);
    });
  });

  describe('resetPassword', () => {
    it('should reset password with valid token', async () => {
      // Arrange
      const resetPasswordDto: ResetPasswordDto = {
        newPassword: 'newPassword123',
        changePasswordToken: 'change-password-token',
      };

      const mockResetResult = {
        code: 200,
        message: 'Đổi mật khẩu thành công',
      };

      jest.spyOn(authService, 'resetPassword').mockResolvedValue(mockResetResult);

      // Act
      const result = await controller.resetPassword(resetPasswordDto);

      // Assert
      expect(authService.resetPassword).toHaveBeenCalledWith(resetPasswordDto);
      expect(result).toEqual(mockResetResult);
    });
  });
});
