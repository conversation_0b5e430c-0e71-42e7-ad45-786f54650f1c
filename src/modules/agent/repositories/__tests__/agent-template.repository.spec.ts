import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { AgentTemplateRepository } from '../agent-template.repository';
import { AgentTemplate } from '@modules/agent/entities';
import { AgentTemplateStatus } from '@modules/agent/constants';

describe('AgentTemplateRepository', () => {
  let repository: AgentTemplateRepository;
  let dataSource: DataSource;
  let queryBuilder: any;

  beforeEach(async () => {
    queryBuilder = {
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
      getMany: jest.fn(),
      getRawMany: jest.fn(),
      getManyAndCount: jest.fn(),
      getCount: jest.fn(),
      offset: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
    };

    dataSource = {
      createEntityManager: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue(queryBuilder),
    } as unknown as DataSource;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentTemplateRepository,
        {
          provide: DataSource,
          useValue: dataSource,
        },
      ],
    }).compile();

    repository = module.get<AgentTemplateRepository>(AgentTemplateRepository);
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(queryBuilder as unknown as SelectQueryBuilder<AgentTemplate>);
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('findById', () => {
    it('should find agent template by id', async () => {
      const id = 'test-id';
      const expectedResult = { id } as AgentTemplate;
      
      queryBuilder.getOne.mockResolvedValue(expectedResult);
      
      const result = await repository.findById(id);
      
      expect(queryBuilder.where).toHaveBeenCalledWith('agentTemplate.id = :id', { id });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByTypeId', () => {
    it('should find agent templates by type id', async () => {
      const typeId = 1;
      const expectedResult = [{ id: 'test-id', typeId }] as AgentTemplate[];
      
      queryBuilder.getMany.mockResolvedValue(expectedResult);
      
      const result = await repository.findByTypeId(typeId);
      
      expect(queryBuilder.where).toHaveBeenCalledWith('agentTemplate.typeId = :typeId', { typeId });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('updateStatus', () => {
    it('should update agent template status', async () => {
      const id = 'test-id';
      const status = AgentTemplateStatus.PUBLISHED;
      const updatedBy = 1;
      
      jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1 } as any);
      
      const result = await repository.updateStatus(id, status, updatedBy);
      
      expect(repository.update).toHaveBeenCalledWith(id, { status, updatedBy });
      expect(result).toEqual(1);
    });
  });
});
