import {Injectable, Logger, NotFoundException} from '@nestjs/common';
import {DataSource, Repository, SelectQueryBuilder} from 'typeorm';
import {AgentRole} from '@modules/agent/entities';
import { PaginatedResult } from '@/common/response';
import { RoleQueryDto, RoleSortBy } from '@modules/agent/admin/dto/agent-role';
import { RoleListResponseDto } from '@modules/agent/admin/dto/agent-role';
import { AvatarUrlHelper } from '@modules/agent/admin/helpers/avatar-url.helper';

/**
 * Repository cho AgentRole
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến vai trò của agent
 */
@Injectable()
export class AgentRoleRepository extends Repository<AgentRole> {
  private readonly logger = new Logger(AgentRoleRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentRole, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentRole
   * @returns SelectQueryBuilder cho AgentRole
   */
  private createBaseQuery(): SelectQueryBuilder<AgentRole> {
    return this.createQueryBuilder('agentRole');
  }

  /**
   * Tìm vai trò theo ID
   * @param id ID của vai trò
   * @returns Vai trò nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentRole | null> {
    return this.createBaseQuery()
      .where('agentRole.id = :id', { id })
      .andWhere('agentRole.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Tìm vai trò theo ID và đảm bảo vai trò tồn tại
   * @param id ID của vai trò
   * @returns Vai trò
   * @throws NotFoundException nếu không tìm thấy vai trò
   */
  async findByIdOrFail(id: string): Promise<AgentRole> {
    const role = await this.findById(id);

    if (!role) {
      throw new NotFoundException(`Vai trò với ID "${id}" không tồn tại`);
    }

    return role;
  }

  /**
   * Tìm vai trò theo tên
   * @param name Tên vai trò
   * @returns Vai trò nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string): Promise<AgentRole | null> {
    return this.createBaseQuery()
      .where('agentRole.name = :name', { name })
      .andWhere('agentRole.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Tìm vai trò theo ID của agent hệ thống
   * @param agentSystemId ID của agent hệ thống
   * @param agentSystemRepository Repository của AgentSystem (optional)
   * @returns Danh sách vai trò
   */
  async findByAgentSystemId(agentSystemId: string, agentSystemRepository?: any): Promise<AgentRole[]> {
    try {
      // Nếu có agentSystemRepository, sử dụng nó để lấy thông tin agent system
      if (agentSystemRepository) {
        const agentSystem = await agentSystemRepository.findById(agentSystemId);
        if (agentSystem && agentSystem.roleId) {
          const role = await this.findById(agentSystem.roleId);
          return role ? [role] : [];
        }
      }

      // Fallback: Tìm trực tiếp trong database
      const roles = await this.createBaseQuery()
        .innerJoin('agents_system', 'agentSystem', 'agentSystem.role_id = agentRole.id')
        .where('agentSystem.id = :agentSystemId', { agentSystemId })
        .andWhere('agentRole.deletedAt IS NULL')
        .getMany();

      return roles;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm vai trò theo agent system ID ${agentSystemId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Phân tích chuỗi JSON moduleMcpConfig
   * @param moduleMcpConfig Cấu hình MCP dạng chuỗi hoặc đối tượng
   * @returns Đối tượng cấu hình MCP đã phân tích
   */
  parseModuleMcpConfig(moduleMcpConfig: any): any {
    if (typeof moduleMcpConfig === 'string') {
      try {
        return JSON.parse(moduleMcpConfig);
      } catch (error) {
        this.logger.warn(`Không thể parse moduleMcpConfig: ${error.message}`);
        return {};
      }
    }
    return moduleMcpConfig || {};
  }

  /**
   * Lấy danh sách tất cả vai trò (không bị xóa mềm)
   * @returns Danh sách vai trò
   */
  async findAllActive(): Promise<AgentRole[]> {
    return this.createBaseQuery()
      .where('agentRole.deletedAt IS NULL')
      .getMany();
  }

  /**
   * Lấy danh sách tất cả vai trò (bao gồm cả bị xóa mềm)
   * @returns Danh sách vai trò
   */
  async findAllWithDeleted(): Promise<AgentRole[]> {
    return this.find({ withDeleted: true });
  }

  /**
   * Lấy danh sách chỉ các vai trò đã bị xóa mềm
   * @returns Danh sách vai trò đã bị xóa mềm
   */
  async findAllDeleted(): Promise<AgentRole[]> {
    return this.createBaseQuery()
      .where('agentRole.deletedAt IS NOT NULL')
      .withDeleted()
      .getMany();
  }

  /**
   * Lấy danh sách vai trò đã xóa với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách vai trò đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentRole> & { deletedEmployees?: any[] }> {
    // Query chính để lấy agent roles đã xóa
    const qb = this.createBaseQuery()
      .where('agentRole.deletedAt IS NOT NULL')
      .withDeleted();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(agentRole.name ILIKE :search OR agentRole.description ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Thêm sắp xếp
    const sortColumn = this.getSortColumn(sortBy as RoleSortBy);
    qb.orderBy(`agentRole.${sortColumn}`, sortDirection);

    // Thêm phân trang
    qb.skip((page - 1) * limit).take(limit);

    // Lấy danh sách agent roles đã xóa
    const [items, total] = await qb.getManyAndCount();

    // Lấy thông tin employees đã xóa (batch query)
    const deletedEmployeeIds = [...new Set(items.map(item => item.deletedBy).filter(Boolean))];
    let deletedEmployees: any[] = [];

    if (deletedEmployeeIds.length > 0) {
      const employeeQb = this.dataSource.createQueryBuilder()
        .select([
          'employee.id',
          'employee.fullName',
          'employee.avatar'
        ])
        .from('employees', 'employee')
        .where('employee.id IN (:...ids)', { ids: deletedEmployeeIds });

      const employeeResults = await employeeQb.getRawMany();

      // Map employee info với agent roles
      deletedEmployees = items.map(agentRole => {
        const employee = employeeResults.find(emp => emp.employee_id === agentRole.deletedBy);
        return {
          agentRoleId: agentRole.id,
          employeeId: agentRole.deletedBy,
          employeeName: employee?.employee_fullName || 'Unknown',
          employeeAvatar: employee?.employee_avatar || null,
        };
      });
    }

    return {
      items,
      deletedEmployees,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tạo vai trò mới
   * @param data Dữ liệu vai trò
   * @returns Vai trò đã được tạo
   */
  async createRole(data: Partial<AgentRole>): Promise<AgentRole> {
    const role = this.create(data);
    return this.save(role);
  }

  /**
   * Tìm các vai trò được tạo trong khoảng thời gian
   * @param startTime Thời gian bắt đầu (timestamp milliseconds)
   * @param endTime Thời gian kết thúc (timestamp milliseconds)
   * @returns Danh sách vai trò
   */
  async findByCreatedTimeRange(
    startTime: number,
    endTime: number,
  ): Promise<AgentRole[]> {
    return this.createBaseQuery()
      .where('agentRole.createdAt >= :startTime', { startTime })
      .andWhere('agentRole.createdAt <= :endTime', { endTime })
      .andWhere('agentRole.deletedAt IS NULL')
      .getMany();
  }

  /**
   * Tìm các vai trò được cập nhật trong khoảng thời gian
   * @param startTime Thời gian bắt đầu (timestamp milliseconds)
   * @param endTime Thời gian kết thúc (timestamp milliseconds)
   * @returns Danh sách vai trò
   */
  async findByUpdatedTimeRange(
    startTime: number,
    endTime: number,
  ): Promise<AgentRole[]> {
    return this.createBaseQuery()
      .where('agentRole.updatedAt >= :startTime', { startTime })
      .andWhere('agentRole.updatedAt <= :endTime', { endTime })
      .andWhere('agentRole.deletedAt IS NULL')
      .getMany();
  }

  /**
   * Tìm các vai trò được xóa mềm trong khoảng thời gian
   * @param startTime Thời gian bắt đầu (timestamp milliseconds)
   * @param endTime Thời gian kết thúc (timestamp milliseconds)
   * @returns Danh sách vai trò
   */
  async findByDeletedTimeRange(
    startTime: number,
    endTime: number,
  ): Promise<AgentRole[]> {
    return this.createBaseQuery()
      .withDeleted()
      .where('agentRole.deletedAt >= :startTime', { startTime })
      .andWhere('agentRole.deletedAt <= :endTime', { endTime })
      .getMany();
  }

  /**
   * Kiểm tra vai trò có đang được sử dụng bởi agent system nào không
   * @param roleId ID của vai trò
   * @param agentSystemRepository Repository của AgentSystem
   * @returns true nếu vai trò đang được sử dụng, false nếu không
   */
  async isRoleInUse(roleId: string, agentSystemRepository: any): Promise<boolean> {
    const agentSystem = await agentSystemRepository.findOne({
      where: { roleId: roleId },
    });

    return !!agentSystem;
  }

  /**
   * Lấy thông tin agent sử dụng vai trò
   * @param roleId ID của vai trò
   * @param agentSystemRepository Repository của AgentSystem
   * @param agentRepository Repository của Agent
   * @param cdnService CdnService để tạo URL avatar (optional)
   * @returns Thông tin agent hoặc null nếu không có
   */
  async getAgentUsingRole(
    roleId: string,
    agentSystemRepository: any,
    agentRepository: any,
    cdnService?: any
  ): Promise<{ id: string; name: string; avatar?: string | null } | null> {
    try {
      // Tìm agent system sử dụng vai trò này
      const agentSystem = await agentSystemRepository.findOne({
        where: { roleId: roleId },
      });

      if (!agentSystem) {
        return null;
      }

      // Lấy thông tin agent từ ID của agent system
      const agent = await agentRepository.findById(agentSystem.id);

      if (!agent) {
        return null;
      }

      // Tạo URL avatar nếu có CdnService và agent có avatar
      let avatarUrl: string | null = null;
      if (cdnService && agent.avatar) {
        try {
          // Sử dụng AvatarUrlHelper để tạo URL xem avatar
          avatarUrl = AvatarUrlHelper.generateViewUrl(cdnService, agent.avatar);
        } catch (avatarError) {
          this.logger.error(`Lỗi khi tạo URL avatar: ${avatarError.message}`, avatarError.stack);

          // Fallback: Sử dụng cdnService trực tiếp
          try {
            avatarUrl = cdnService.generateUrlView(agent.avatar);
          } catch (fallbackError) {
            this.logger.error(`Lỗi khi tạo URL avatar fallback: ${fallbackError.message}`, fallbackError.stack);
          }
        }
      }

      return {
        id: agent.id,
        name: agent.name,
        avatar: avatarUrl
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin agent sử dụng vai trò: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Cập nhật vai trò
   * @param id ID của vai trò
   * @param data Dữ liệu cập nhật
   * @returns Vai trò đã được cập nhật
   */
  async updateRole(id: string, data: Partial<AgentRole>): Promise<AgentRole> {
    const role = await this.findByIdOrFail(id);

    const updatedRole = this.create({
      ...role,
      ...data,
      updatedBy: data.updatedBy || role.updatedBy,
    });

    return this.save(updatedRole);
  }

  /**
   * Xóa mềm vai trò
   * @param id ID của vai trò
   * @param deletedBy ID của người xóa
   * @returns Vai trò đã được xóa mềm
   */
  async softDeleteAndUpdate(id: string, deletedBy?: number): Promise<AgentRole | null> {
    const role = await this.findByIdOrFail(id);

    // Cập nhật thời gian xóa và người xóa
    role.deletedAt = Date.now();
    if (deletedBy) {
      role.deletedBy = deletedBy;
    }

    // Lưu thay đổi
    await this.save(role);

    return this.findOne({ where: { id }, withDeleted: true });
  }

  /**
   * Khôi phục vai trò đã bị xóa mềm
   * @param id ID của vai trò
   * @param updatedBy ID của người khôi phục
   * @returns Vai trò đã được khôi phục
   */
  async restoreAndUpdate(id: string, updatedBy?: number): Promise<AgentRole | null> {
    // Tìm vai trò đã bị xóa mềm
    const role = await this.findOne({ where: { id }, withDeleted: true });

    if (!role) {
      throw new NotFoundException(`Vai trò với ID "${id}" không tồn tại`);
    }

    // Khôi phục vai trò bằng cách đặt deletedAt và deletedBy về null
    role.deletedAt = null;
    role.deletedBy = null;

    // Cập nhật thông tin người khôi phục nếu có
    if (updatedBy) {
      role.updatedBy = updatedBy;
      role.updatedAt = Date.now();
    }

    // Lưu thay đổi
    await this.save(role);

    return this.findOne({ where: { id } });
  }

  /**
   * Lấy danh sách vai trò với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách vai trò với phân trang
   */
  async findPaginatedRoles(
    queryDto: RoleQueryDto,
  ): Promise<PaginatedResult<RoleListResponseDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy,
        sortDirection,
      } = queryDto;

      // Tạo query builder
      const queryBuilder = this.createQueryBuilder('role')
        .select([
          'role.id as id',
          'role.name as name',
          'role.description as description',
        ])
        .where('role.deleted_at IS NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere(
          '(role.name ILIKE :search OR role.description ILIKE :search)',
          {
            search: `%${search}%`,
          },
        );
      }

      // Thêm sắp xếp
      const sortColumn = this.getSortColumn(sortBy);
      const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(`role.${sortColumn}`, direction as 'ASC' | 'DESC');

      // Thêm phân trang
      const skip = (page - 1) * limit;
      queryBuilder.offset(skip).limit(limit);

      // Thực hiện truy vấn
      const [roles, totalItems] = await Promise.all([
        queryBuilder.getRawMany(),
        queryBuilder.getCount(),
      ]);

      // Chuyển đổi kết quả sang DTO đơn giản
      const items: RoleListResponseDto[] = roles.map((role) => ({
        id: role.id,
        name: role.name,
        description: role.description || '',
      }));

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách vai trò: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi tên trường sắp xếp thành tên cột trong database
   * @param sortBy Tên trường sắp xếp
   * @returns Tên cột trong database
   */
  private getSortColumn(sortBy?: RoleQueryDto['sortBy']): string {
    if (!sortBy) return 'created_at';

    switch (sortBy) {
      case RoleSortBy.NAME:
        return 'name';
      case RoleSortBy.CREATED_AT:
      default:
        return 'created_at';
    }
  }

  /**
   * Thống kê số lượng vai trò được tạo theo tháng
   * @param year Năm cần thống kê
   * @returns Thống kê số lượng vai trò theo tháng
   */
  async getRoleCreationStatsByMonth(
    year: number,
  ): Promise<{ month: number; count: number }[]> {
    // Tính timestamp đầu năm và cuối năm
    const startOfYear = new Date(year, 0, 1).getTime();
    const endOfYear = new Date(year, 11, 31, 23, 59, 59, 999).getTime();

    const result = await this.createQueryBuilder('agentRole')
      .select(
        "EXTRACT(MONTH FROM agentRole.createdAt / 1000 * INTERVAL '1 second')",
        'month',
      )
      .addSelect('COUNT(agentRole.id)', 'count')
      .where('agentRole.createdAt >= :startOfYear', { startOfYear })
      .andWhere('agentRole.createdAt <= :endOfYear', { endOfYear })
      .groupBy('month')
      .orderBy('month', 'ASC')
      .getRawMany();

    return result.map((item) => ({
      month: parseInt(item.month),
      count: parseInt(item.count),
    }));
  }

  /**
   * Tìm vai trò theo thời gian tạo
   * @deprecated Phương thức này đã được sửa đổi vì không có cột agent_system_id
   * @param agentSystemId ID của agent hệ thống (không còn sử dụng)
   * @param startTime Thời gian bắt đầu (timestamp milliseconds)
   * @param endTime Thời gian kết thúc (timestamp milliseconds)
   * @returns Danh sách vai trò
   */
  async findByAgentSystemIdAndTimeRange(
    _agentSystemId: string,
    startTime: number,
    endTime: number,
  ): Promise<AgentRole[]> {
    return this.createBaseQuery()
      .where('agentRole.createdAt >= :startTime', { startTime })
      .andWhere('agentRole.createdAt <= :endTime', { endTime })
      .andWhere('agentRole.deletedAt IS NULL')
      .getMany();
  }
}
