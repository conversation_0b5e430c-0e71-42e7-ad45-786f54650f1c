import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentBaseRepository } from '@modules/agent/repositories/agent-base.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { MultiAgentsSystemRepository } from '@modules/agent/repositories/multi-agents-system.repository';
import { Injectable, Logger } from '@nestjs/common';
import {
  CreateMultiAgentsSystemDto,
  MultiAgentsSystemResponseDto,
  QueryMultiAgentsSystemDto,
  UpdateMultiAgentsSystemDto,
} from '../dto/multi-agents-system';

/**
 * Service xử lý logic nghiệp vụ cho quan hệ đa cấp giữa các agent
 */
@Injectable()
export class MultiAgentsSystemService {
  private readonly logger = new Logger(MultiAgentsSystemService.name);

  constructor(
    private readonly multiAgentsSystemRepository: MultiAgentsSystemRepository,
    private readonly agentBaseRepository: AgentBaseRepository,
    private readonly agentSystemRepository: AgentSystemRepository,
  ) { }

  /**
   * Tạo mới quan hệ đa cấp giữa các agent
   * @param employeeId ID của nhân viên thực hiện hành động
   * @param createDto Thông tin quan hệ cần tạo
   * @returns Thông tin quan hệ đã tạo
   */
  async createRelation(
    createDto: CreateMultiAgentsSystemDto,
  ): Promise<MultiAgentsSystemResponseDto> {
    try {
      this.logger.debug(`Tạo quan hệ đa cấp giữa agent ${createDto.parentAgentId} và ${createDto.childAgentId}`);

      // Kiểm tra agent cấp trên có tồn tại không
      const parentAgent = await this.agentBaseRepository.findById(createDto.parentAgentId);
      if (!parentAgent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
      }

      // Kiểm tra agent cấp dưới có tồn tại không
      const childAgent = await this.agentSystemRepository.findById(createDto.childAgentId);
      if (!childAgent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
      }

      // Tạo quan hệ mới
      const relation = await this.multiAgentsSystemRepository.createRelation(
        createDto.parentAgentId,
        createDto.childAgentId,
        createDto.prompt,
      );

      return MultiAgentsSystemResponseDto.fromEntity(relation);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo quan hệ đa cấp: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.RELATION_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật prompt cho quan hệ đa cấp
   * @param employeeId ID của nhân viên thực hiện hành động
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   * @param updateDto Thông tin cần cập nhật
   * @returns Thông tin quan hệ sau khi cập nhật
   */
  async updateRelation(
    employeeId: number,
    parentAgentId: string,
    childAgentId: string,
    updateDto: UpdateMultiAgentsSystemDto,
  ): Promise<MultiAgentsSystemResponseDto> {
    try {
      this.logger.debug(`Cập nhật prompt cho quan hệ giữa agent ${parentAgentId} và ${childAgentId}`);

      // Kiểm tra quan hệ có tồn tại không
      const relation = await this.multiAgentsSystemRepository.findByParentAndChildIds(parentAgentId, childAgentId);
      if (!relation) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      // Cập nhật prompt
      await this.multiAgentsSystemRepository.updatePrompt(
        parentAgentId,
        childAgentId,
        updateDto.prompt,
      );

      // Lấy thông tin quan hệ sau khi cập nhật
      const updatedRelation = await this.multiAgentsSystemRepository.findByParentAndChildIds(parentAgentId, childAgentId);
      if (!updatedRelation) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      return MultiAgentsSystemResponseDto.fromEntity(updatedRelation);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật quan hệ đa cấp: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.RELATION_UPDATE_FAILED);
    }
  }

  /**
   * Xóa quan hệ đa cấp
   * @param employeeId ID của nhân viên thực hiện hành động
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   */
  async deleteRelation(
    employeeId: number,
    parentAgentId: string,
    childAgentId: string,
  ): Promise<void> {
    try {
      this.logger.debug(`Xóa quan hệ giữa agent ${parentAgentId} và ${childAgentId}`);

      // Kiểm tra quan hệ có tồn tại không
      const relation = await this.multiAgentsSystemRepository.findByParentAndChildIds(parentAgentId, childAgentId);
      if (!relation) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      // Xóa quan hệ
      await this.multiAgentsSystemRepository.deleteRelation(parentAgentId, childAgentId);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa quan hệ đa cấp: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.RELATION_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách quan hệ đa cấp với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách quan hệ đa cấp với phân trang
   */
  async getRelations(
    queryDto: QueryMultiAgentsSystemDto,
  ): Promise<PaginatedResult<MultiAgentsSystemResponseDto>> {
    try {
      this.logger.debug('Lấy danh sách quan hệ đa cấp');

      // Lấy danh sách quan hệ
      const result = await this.multiAgentsSystemRepository.findPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.parentAgentId,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Chuyển đổi từ entity sang DTO
      const items = result.items.map(item => MultiAgentsSystemResponseDto.fromEntity(item));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách quan hệ đa cấp: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.RELATION_QUERY_FAILED);
    }
  }

  /**
   * Lấy thông tin quan hệ đa cấp theo ID
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   * @returns Thông tin quan hệ đa cấp
   */
  async getRelation(
    parentAgentId: string,
    childAgentId: string,
  ): Promise<MultiAgentsSystemResponseDto> {
    try {
      this.logger.debug(`Lấy thông tin quan hệ giữa agent ${parentAgentId} và ${childAgentId}`);

      // Lấy thông tin quan hệ
      const relation = await this.multiAgentsSystemRepository.findByParentAndChildIds(parentAgentId, childAgentId);
      if (!relation) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      return MultiAgentsSystemResponseDto.fromEntity(relation);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin quan hệ đa cấp: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.RELATION_QUERY_FAILED);
    }
  }
}
