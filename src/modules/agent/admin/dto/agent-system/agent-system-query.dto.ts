import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của agent system
 */
export enum AgentSystemSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  STATUS = 'status',
  NAME_CODE = 'nameCode'
}

/**
 * DTO cho việc truy vấn danh sách agent system
 */
export class AgentSystemQueryDto extends QueryDto {

  /**
   * Lọc theo trạng thái (chỉ cho phép DRAFT hoặc APPROVED)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái (chỉ cho phép DRAFT hoặc APPROVED)',
    enum: [AgentStatusEnum.DRAFT, AgentStatusEnum.APPROVED],
  })
  @IsEnum([AgentStatusEnum.DRAFT, AgentStatusEnum.APPROVED], {
    message: 'status chỉ có thể là DRAFT hoặc APPROVED'
  })
  @IsOptional()
  status?: AgentStatusEnum;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentSystemSortBy,
    example: AgentSystemSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentSystemSortBy)
  sortBy?: AgentSystemSortBy = AgentSystemSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
