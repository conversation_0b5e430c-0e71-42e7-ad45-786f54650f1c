import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';
import { RankStrategyStatus } from '../../entities/rank-strategy.entity';

/**
 * DTO cho việc query danh sách cấp bậc chiến lư<PERSON>
 */
export class QueryRankStrategyDto extends QueryDto {
  @ApiProperty({
    description: 'Tên cấp bậc chiến lược',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Trạng thái cấp bậc chiến lư<PERSON>',
    enum: RankStrategyStatus,
    required: false,
  })
  @IsEnum(RankStrategyStatus)
  @IsOptional()
  status?: RankStrategyStatus;
}
