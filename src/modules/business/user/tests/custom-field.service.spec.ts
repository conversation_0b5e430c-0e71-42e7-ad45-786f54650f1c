import { Test, TestingModule } from '@nestjs/testing';
import { CustomFieldService } from '../services';
import { CustomFieldRepository, CustomGroupFormRepository, CustomGroupFormFieldRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../helpers';
import { CreateCustomFieldDto } from '../dto';
import { CustomField, CustomGroupForm, CustomGroupFormField } from '@modules/business/entities';
import { EntityStatusEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

describe('CustomFieldService', () => {
  let service: CustomFieldService;
  let customFieldRepository: CustomFieldRepository;
  let customGroupFormRepository: CustomGroupFormRepository;
  let customGroupFormFieldRepository: CustomGroupFormFieldRepository;
  let validationHelper: ValidationHelper;

  const mockCustomFieldRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockCustomGroupFormRepository = {
    findById: jest.fn(),
  };

  const mockCustomGroupFormFieldRepository = {
    save: jest.fn(),
  };

  const mockValidationHelper = {
    validateCreateCustomFieldData: jest.fn(),
    validateConfigIdNotExists: jest.fn(),
    validateGroupFormExists: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomFieldService,
        {
          provide: CustomFieldRepository,
          useValue: mockCustomFieldRepository,
        },
        {
          provide: CustomGroupFormRepository,
          useValue: mockCustomGroupFormRepository,
        },
        {
          provide: CustomGroupFormFieldRepository,
          useValue: mockCustomGroupFormFieldRepository,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
      ],
    }).compile();

    service = module.get<CustomFieldService>(CustomFieldService);
    customFieldRepository = module.get<CustomFieldRepository>(CustomFieldRepository);
    customGroupFormRepository = module.get<CustomGroupFormRepository>(CustomGroupFormRepository);
    customGroupFormFieldRepository = module.get<CustomGroupFormFieldRepository>(CustomGroupFormFieldRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a custom field successfully', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
      };

      const savedCustomField = {
        id: 2,
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
        employeeId: null,
        status: EntityStatusEnum.PENDING,
        createAt: 1741708800000,
      };

      mockCustomFieldRepository.findOne.mockResolvedValue(null);
      mockCustomFieldRepository.save.mockResolvedValue(savedCustomField);

      // Act
      const result = await service.create(createDto);

      // Assert
      expect(validationHelper.validateCreateCustomFieldData).toHaveBeenCalledWith(createDto);
      expect(customFieldRepository.findOne).toHaveBeenCalledWith({
        where: { configId: createDto.configId },
      });
      expect(validationHelper.validateConfigIdNotExists).toHaveBeenCalledWith(null, createDto.configId);
      expect(customFieldRepository.save).toHaveBeenCalled();
      expect(result).toEqual(savedCustomField);
    });

    it('should create a custom field and link it to a group form', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
        formGroupId: 1,
        grid: { i: 'field1', x: 0, y: 0, w: 6, h: 2 },
        value: { value: 'example' },
      };

      const formGroup = new CustomGroupForm();
      formGroup.id = 1;
      formGroup.label = 'Thông tin cá nhân';
      formGroup.userId = 1001;

      const savedCustomField = {
        id: 2,
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
        employeeId: null,
        status: EntityStatusEnum.PENDING,
        createAt: 1741708800000,
      };

      const savedGroupFormField = {
        fieldId: 2,
        formGroupId: 1,
        gird: { i: 'field1', x: 0, y: 0, w: 6, h: 2 },
        value: { value: 'example' },
      };

      mockCustomFieldRepository.findOne.mockResolvedValue(null);
      mockCustomGroupFormRepository.findById.mockResolvedValue(formGroup);
      mockCustomFieldRepository.save.mockResolvedValue(savedCustomField);
      mockCustomGroupFormFieldRepository.save.mockResolvedValue(savedGroupFormField);

      // Act
      const result = await service.create(createDto);

      // Assert
      expect(validationHelper.validateCreateCustomFieldData).toHaveBeenCalledWith(createDto);
      expect(customFieldRepository.findOne).toHaveBeenCalledWith({
        where: { configId: createDto.configId },
      });
      expect(validationHelper.validateConfigIdNotExists).toHaveBeenCalledWith(null, createDto.configId);
      expect(customGroupFormRepository.findById).toHaveBeenCalledWith(createDto.formGroupId);
      expect(validationHelper.validateGroupFormExists).toHaveBeenCalledWith(formGroup, createDto.formGroupId);
      expect(customFieldRepository.save).toHaveBeenCalled();
      expect(customGroupFormFieldRepository.save).toHaveBeenCalled();
      expect(result).toEqual(savedCustomField);
    });

    it('should throw an exception if configId already exists', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
      };

      const existingField = new CustomField();
      existingField.id = 1;
      existingField.configId = 'custom-text-001';

      mockCustomFieldRepository.findOne.mockResolvedValue(existingField);
      mockValidationHelper.validateConfigIdNotExists.mockImplementation(() => {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
          `Config ID '${createDto.configId}' đã tồn tại`,
        );
      });

      // Act & Assert
      await expect(service.create(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateCustomFieldData).toHaveBeenCalledWith(createDto);
      expect(customFieldRepository.findOne).toHaveBeenCalledWith({
        where: { configId: createDto.configId },
      });
      expect(validationHelper.validateConfigIdNotExists).toHaveBeenCalledWith(existingField, createDto.configId);
    });

    it('should throw an exception if group form does not exist', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
        formGroupId: 999,
      };

      mockCustomFieldRepository.findOne.mockResolvedValue(null);
      mockCustomGroupFormRepository.findById.mockResolvedValue(null);
      mockValidationHelper.validateGroupFormExists.mockImplementation(() => {
        throw new AppException(
          BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND,
          `Không tìm thấy nhóm trường tùy chỉnh với ID ${createDto.formGroupId}`,
        );
      });

      // Act & Assert
      await expect(service.create(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateCustomFieldData).toHaveBeenCalledWith(createDto);
      expect(customFieldRepository.findOne).toHaveBeenCalledWith({
        where: { configId: createDto.configId },
      });
      expect(validationHelper.validateConfigIdNotExists).toHaveBeenCalledWith(null, createDto.configId);
      expect(customGroupFormRepository.findById).toHaveBeenCalledWith(createDto.formGroupId);
      expect(validationHelper.validateGroupFormExists).toHaveBeenCalledWith(null, createDto.formGroupId);
    });
  });
});
