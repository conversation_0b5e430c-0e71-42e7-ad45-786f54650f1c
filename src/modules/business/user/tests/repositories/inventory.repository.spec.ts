import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { InventoryRepository } from '../../../repositories';
import { Inventory } from '../../../entities';

describe('InventoryRepository', () => {
  let repository: InventoryRepository;
  let dataSource: DataSource;

  // Mock data
  const mockInventories: Inventory[] = [
    {
      id: 1,
      productId: 1,
      warehouseId: 1,
      currentQuantity: 100,
      totalQuantity: 150,
      availableQuantity: 90,
      reservedQuantity: 5,
      defectiveQuantity: 5,
      lastUpdated: 1715270400000,
    },
    {
      id: 2,
      productId: 2,
      warehouseId: 1,
      currentQuantity: 200,
      totalQuantity: 250,
      availableQuantity: 180,
      reservedQuantity: 10,
      defectiveQuantity: 10,
      lastUpdated: 1715270500000,
    },
    {
      id: 3,
      productId: 1,
      warehouseId: 2,
      currentQuantity: 300,
      totalQuantity: 350,
      availableQuantity: 270,
      reservedQuantity: 15,
      defectiveQuantity: 15,
      lastUpdated: 1715270600000,
    },
  ];

  // Mock query builder
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    getMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getCount: jest.fn().mockImplementation(() => Promise.resolve(0)),
    getManyAndCount: jest.fn().mockImplementation(() => Promise.resolve([[], 0])),
  } as unknown as SelectQueryBuilder<Inventory>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InventoryRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<InventoryRepository>(InventoryRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);
    jest.spyOn(repository, 'create').mockImplementation((data: any) => data as Inventory);
    jest.spyOn(repository, 'save').mockImplementation((inventory: Inventory) => Promise.resolve({ ...inventory, id: inventory.id || 4 }));
    jest.spyOn(repository, 'findOne').mockImplementation((options: any) => {
      const id = options.where?.id;
      const inventory = mockInventories.find(i => i.id === id);
      return Promise.resolve(inventory || null);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createBaseQuery', () => {
    it('nên tạo query builder cơ bản', () => {
      // Act
      const result = (repository as any).createBaseQuery();

      // Assert
      expect(repository.createQueryBuilder).toHaveBeenCalledWith('inventory');
      expect(result).toBe(mockQueryBuilder);
    });
  });

  describe('findById', () => {
    it('nên tìm tồn kho theo ID thành công', async () => {
      // Arrange
      const id = 1;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(mockInventories[0]));

      // Act
      const result = await repository.findById(id);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('inventory.id = :id', { id });
      expect(result).toEqual(mockInventories[0]);
    });

    it('nên trả về null khi không tìm thấy tồn kho', async () => {
      // Arrange
      const id = 999;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(null));

      // Act
      const result = await repository.findById(id);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('inventory.id = :id', { id });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm tồn kho thất bại', async () => {
      // Arrange
      const id = 1;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findById(id)).rejects.toThrow(
        `Lỗi khi tìm tồn kho theo ID ${id}: Database error`
      );
    });
  });

  describe('findByProductAndWarehouse', () => {
    it('nên tìm tồn kho theo productId và warehouseId thành công', async () => {
      // Arrange
      const productId = 1;
      const warehouseId = 1;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(mockInventories[0]));

      // Act
      const result = await repository.findByProductAndWarehouse(productId, warehouseId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('inventory.productId = :productId', { productId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('inventory.warehouseId = :warehouseId', { warehouseId });
      expect(result).toEqual(mockInventories[0]);
    });

    it('nên ném lỗi khi tìm tồn kho thất bại', async () => {
      // Arrange
      const productId = 1;
      const warehouseId = 1;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findByProductAndWarehouse(productId, warehouseId)).rejects.toThrow(
        `Lỗi khi tìm tồn kho theo ID sản phẩm ${productId} và ID kho ${warehouseId}: Database error`
      );
    });
  });



  describe('createInventory', () => {
    it('nên tạo tồn kho mới thành công', async () => {
      // Arrange
      const newInventory: Inventory = {
        productId: 3,
        warehouseId: 2,
        currentQuantity: 400,
        totalQuantity: 450,
        availableQuantity: 360,
        reservedQuantity: 20,
        defectiveQuantity: 20,
        lastUpdated: 1715270700000,
      } as Inventory;

      // Act
      const result = await repository.createInventory(newInventory);

      // Assert
      expect(repository.save).toHaveBeenCalledWith(newInventory);
      expect(result).toEqual({ ...newInventory, id: 4 });
    });


  });

  describe('updateInventory', () => {
    it('nên cập nhật tồn kho thành công', async () => {
      // Arrange
      const id = 1;
      const updateData = {
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
        currentQuantity: 120,
        totalQuantity: 170,
        lastUpdated: 1715270800000,
      };
      jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      // Mock findById để tránh lỗi khi gọi trong updateInventory
      jest.spyOn(repository, 'findById').mockResolvedValue(mockInventories[0]);

      // Act
      const result = await repository.updateInventory(id, updateData);

      // Assert
      expect(repository.update).toHaveBeenCalledWith(id, updateData);
      expect(result).toBeTruthy();
    });


  });

  describe('deleteInventory', () => {
    it('nên xóa tồn kho thành công', async () => {
      // Arrange
      const id = 1;
      jest.spyOn(repository, 'delete').mockResolvedValue({ affected: 1, raw: {} });

      // Act
      const result = await repository.deleteInventory(id);

      // Assert
      expect(repository.delete).toHaveBeenCalledWith(id);
      expect(result).toBeTruthy();
    });


  });
});
