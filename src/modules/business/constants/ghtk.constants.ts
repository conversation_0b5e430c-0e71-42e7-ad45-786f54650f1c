/**
 * Constants cho GHTK API
 */

/**
 * Base URLs cho GHTK API
 */
export const GHTK_BASE_URLS = {
  TEST: 'https://services.giaohangtietkiem.vn',
  PRODUCTION: 'https://services.giaohangtietkiem.vn'
} as const;

/**
 * GHTK API Endpoints
 */
export const GHTK_ENDPOINTS = {
  // Gi<PERSON>i pháp
  GET_SOLUTIONS: '/open/api/v1/shop/solutions',
  
  // Đơn hàng
  CREATE_ORDER: '/services/shipment/order',
  CALCULATE_FEE: '/services/shipment/fee',
  GET_ORDER_STATUS: '/services/shipment/v2',
  PRINT_LABEL: '/services/label',
  CANCEL_ORDER: '/services/shipment/cancel',
  
  // Địa chỉ
  GET_PICKUP_ADDRESSES: '/services/shipment/list_pick_add',
  GET_LEVEL4_ADDRESS: '/services/address/getAddressLevel4',
  
  // <PERSON><PERSON>n phẩm
  SEARCH_PRODUCTS: '/services/kho-hang/thong-tin-san-pham'
} as const;

/**
 * GHTK Default Headers
 */
export const GHTK_DEFAULT_HEADERS = {
  'Content-Type': 'application/json'
} as const;

/**
 * GHTK Test Configuration
 * Sử dụng token test từ tài liệu GHTK
 */
export const GHTK_TEST_CONFIG = {
  TOKEN: 'APITokenSample-ca441e70288cB0515F310742',
  PARTNER_CODE: 'PARTNER_CODE_SAMPLE',
  BASE_URL: GHTK_BASE_URLS.TEST
} as const;



/**
 * GHTK Transport Methods
 */
export const GHTK_TRANSPORT_METHODS = {
  ROAD: 'road',
  FLY: 'fly'
} as const;



/**
 * GHTK Error Messages
 */
export const GHTK_ERROR_MESSAGES = {
  INVALID_TOKEN: 'Token không hợp lệ',
  INVALID_PARTNER_CODE: 'Mã đối tác không hợp lệ',
  ORDER_NOT_FOUND: 'Không tìm thấy đơn hàng',
  CANNOT_CANCEL: 'Không thể hủy đơn hàng',
  INVALID_ADDRESS: 'Địa chỉ không hợp lệ',
  INVALID_WEIGHT: 'Trọng lượng không hợp lệ',
  INVALID_VALUE: 'Giá trị đơn hàng không hợp lệ',
  API_ERROR: 'Lỗi khi gọi API GHTK',
  NETWORK_ERROR: 'Lỗi kết nối mạng',
  TIMEOUT_ERROR: 'Timeout khi gọi API'
} as const;

/**
 * GHTK Request Timeout (milliseconds)
 */
export const GHTK_TIMEOUT = 30000; // 30 seconds


