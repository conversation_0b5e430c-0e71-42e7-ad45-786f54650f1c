import { Controller, Get, Post, Put, Body, Param, Query, ParseIntPipe, UseGuards, Request } from '@nestjs/common';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse as ApiResponseDoc, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { SubscriptionUserService } from '@modules/subscription/user/services';
import {
  SubscriptionFilterDto,
  SubscriptionCreateDto,
  SubscriptionResponseDto,
  AutoRenewUpdateDto,
  ChangePlanDto,
  UsageLogFilterDto,
  UsageLogResponseDto,
  SubscriptionExtendDto
} from '../../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { CurrentUser } from '@modules/auth/decorators';

@ApiTags(SWAGGER_API_TAGS.USER_SUBSCRIPTIONS)
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('user/subscriptions')
export class SubscriptionUserController {
  constructor(private readonly subscriptionUserService: SubscriptionUserService) {}

  @ApiOperation({ summary: 'Lấy danh sách đăng ký của người dùng' })
  @ApiQuery({ type: SubscriptionFilterDto })
  @Get()
  async findAll(@CurrentUser() user: JwtPayload, @Query() filterDto: SubscriptionFilterDto) {
    const { status, page, limit } = filterDto;
    const result = await this.subscriptionUserService.findSubscriptions(user.id, status || null, page, limit);

    const responseData = {
      items: result.items.map(item => {
        const dto = new SubscriptionResponseDto(item);
        if ((item as any).planInfo) {
          dto.planInfo = (item as any).planInfo;
        }
        if ((item as any).pricingInfo) {
          dto.pricingInfo = (item as any).pricingInfo;
        }
        return dto;
      }),
      meta: result.meta
    };

    return ApiResponseDto.success(responseData);
  }

  @ApiOperation({ summary: 'Lấy chi tiết đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký', type: Number })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy đăng ký' })
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const { subscription, planInfo, pricingInfo } = await this.subscriptionUserService.findSubscriptionWithDetails(id);

    return ApiResponseDto.success(new SubscriptionResponseDto(subscription, planInfo, pricingInfo));
  }

  @ApiOperation({ summary: 'Đăng ký gói dịch vụ' })
  @ApiResponseDoc({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy tùy chọn giá' })
  @Post()
  async create(@CurrentUser() user: JwtPayload, @Body() createDto: SubscriptionCreateDto) {
    const subscription = await this.subscriptionUserService.createSubscription(
      user.id,
      createDto.planPricingId,
      createDto.autoRenew
    );

    return ApiResponseDto.created(new SubscriptionResponseDto(subscription), 'Subscription created successfully');
  }

  @ApiOperation({ summary: 'Hủy đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký', type: Number })
  @ApiResponseDoc({ status: 400, description: 'Chỉ có thể hủy đăng ký đang hoạt động' })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy đăng ký' })
  @Put(':id/cancel')
  async cancel(@Param('id', ParseIntPipe) id: number) {
    const subscription = await this.subscriptionUserService.cancelSubscription(id);

    const responseData = {
      id: subscription.id,
      status: subscription.status,
      updatedAt: subscription.updatedAt
    };

    return ApiResponseDto.success(responseData, 'Subscription cancelled successfully');
  }

  @ApiOperation({ summary: 'Bật/tắt tự động gia hạn' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký', type: Number })
  @ApiResponseDoc({ status: 400, description: 'Chỉ có thể cập nhật đăng ký đang hoạt động' })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy đăng ký' })
  @Put(':id/auto-renew')
  async updateAutoRenew(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: AutoRenewUpdateDto
  ) {
    const subscription = await this.subscriptionUserService.updateAutoRenew(id, updateDto.autoRenew);

    const responseData = {
      id: subscription.id,
      autoRenew: subscription.autoRenew,
      updatedAt: subscription.updatedAt
    };

    return ApiResponseDto.success(responseData, 'Auto-renew setting updated successfully');
  }

  @ApiOperation({ summary: 'Nâng cấp/hạ cấp đăng ký' })
  @ApiParam({ name: 'id', description: 'ID của đăng ký', type: Number })
  @ApiResponseDoc({ status: 400, description: 'Chỉ có thể thay đổi đăng ký đang hoạt động' })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy đăng ký hoặc tùy chọn giá' })
  @Put(':id/change-plan')
  async changePlan(
    @Param('id', ParseIntPipe) id: number,
    @Body() changeDto: ChangePlanDto
  ) {
    const subscription = await this.subscriptionUserService.changePlan(
      id,
      changeDto.newPlanPricingId,
      changeDto.effectiveImmediately
    );

    const { planInfo, pricingInfo } = await this.subscriptionUserService.findSubscriptionWithDetails(id);

    return ApiResponseDto.success(
      new SubscriptionResponseDto(subscription, planInfo, pricingInfo),
      'Subscription plan changed successfully'
    );
  }

  @ApiOperation({ summary: 'Lấy lịch sử sử dụng' })
  @ApiParam({ name: 'subscriptionId', description: 'ID của đăng ký', type: Number })
  @ApiQuery({ type: UsageLogFilterDto })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy đăng ký' })
  @Get(':subscriptionId/usage-logs')
  async findUsageLogs(
    @Param('subscriptionId', ParseIntPipe) subscriptionId: number,
    @Query() filterDto: UsageLogFilterDto
  ) {
    const { startDate, endDate, page, limit } = filterDto;
    const result = await this.subscriptionUserService.findUsageLogs(
      subscriptionId,
      startDate || null,
      endDate || null,
      page,
      limit
    );

    const responseData = {
      items: result.items.map(item => new UsageLogResponseDto(item)),
      meta: result.meta
    };

    return ApiResponseDto.success(responseData);
  }

  /**
   * Gia hạn đăng ký
   * @param id ID của đăng ký
   * @param extendDto Thông tin gia hạn
   * @returns Đăng ký đã được gia hạn
   */
  @ApiOperation({ summary: 'Gia hạn đăng ký' })
  @ApiResponseDoc({ status: 404, description: 'Không tìm thấy đăng ký' })
  @ApiResponseDoc({ status: 400, description: 'Không thể gia hạn đăng ký với loại gói khác nhau' })
  @Put(':id/extend')
  async extendSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Body() extendDto: SubscriptionExtendDto
  ) {
    const subscription = await this.subscriptionUserService.extendSubscription(
      id,
      extendDto.planPricingId,
      extendDto.autoRenew
    );

    const { planInfo, pricingInfo } = await this.subscriptionUserService.findSubscriptionWithDetails(id);

    return ApiResponseDto.success(
      new SubscriptionResponseDto(subscription, planInfo, pricingInfo),
      'Đăng ký đã được gia hạn thành công'
    );
  }
}
