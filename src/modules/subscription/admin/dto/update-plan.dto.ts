import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';
import { PackageType } from '@modules/subscription/enums';

export class UpdatePlanDto {
  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Premium Plan',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'Mô tả gói dịch vụ',
    example: 'G<PERSON>i dịch vụ cao cấp với nhiều tính năng',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại gói dịch vụ',
    enum: PackageType,
    example: PackageType.TIME_ONLY,
    required: false
  })
  @IsOptional()
  @IsEnum(PackageType)
  packageType?: PackageType;
}
