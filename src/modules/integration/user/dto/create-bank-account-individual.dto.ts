import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho việc tạo mới tài khoản ngân hàng cá nhân
 */
export class CreateBankAccountIndividualDto {
  @ApiProperty({
    description: 'ID ngân hàng',
    example: '1'
  })
  @IsNotEmpty()
  @IsString()
  bankId: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  accountHolderName: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(5)
  @MaxLength(30)
  accountNumber: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> gợi nhớ',
    example: 'Tà<PERSON> khoản chính'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  label: string;

  @ApiProperty({
    description: 'Số CMND/CCCD',
    example: '************',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  identificationNumber?: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '**********',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(15)
  phoneNumber?: string;

  @ApiProperty({
    description: 'Tên điểm bán',
    example: 'Cửa hàng ABC',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  merchantName?: string;

  @ApiProperty({
    description: 'Địa chỉ điểm bán',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  merchantAddress?: string;
}
