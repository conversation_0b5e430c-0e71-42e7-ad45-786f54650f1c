import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho việc tạo mới tài khoản ngân hàng
 */
export class CreateBankAccountDto {
  @ApiProperty({
    description: 'ID ngân hàng',
    example: '1'
  })
  @IsNotEmpty()
  @IsString()
  bankId: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  accountHolderName: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(5)
  @MaxLength(30)
  accountNumber: string;

  @ApiProperty({
    description: 'Tên gợi nhớ',
    example: '<PERSON><PERSON><PERSON> kho<PERSON>n chính'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  label: string;
}
