# User Provider Shipment API Examples

## Overview
API để quản lý cấu hình shipping provider (GHTK, GHN) của người dùng.

## Base URL
```
/v1/user/provider-shipments
```

## Authentication
Tất cả API đều yêu cầu JWT token trong header:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### 1. T<PERSON>o cấu hình GHTK

**POST** `/v1/user/provider-shipments`

**Request Body:**
```json
{
  "name": "C<PERSON>u hình GHTK chính",
  "type": "GHTK",
  "ghtkConfig": {
    "token": "8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou",
    "baseUrl": "https://services-staging.ghtklab.com",
    "timeout": 30000,
    "testMode": true
  }
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "name": "<PERSON><PERSON><PERSON> hình GHTK chính",
  "type": "GHTK",
  "ghtkConfig": {
    "baseUrl": "https://services-staging.ghtklab.com",
    "timeout": 30000,
    "testMode": true,
    "hasToken": true
  },
  "createdAt": 1640995200000
}
```

### 2. Tạo cấu hình GHN

**POST** `/v1/user/provider-shipments`

**Request Body:**
```json
{
  "name": "Cấu hình GHN chính",
  "type": "GHN",
  "ghnConfig": {
    "token": "42d0fc57-402d-11f0-9b81-222185cb68c8",
    "shopId": "196768",
    "baseUrl": "https://dev-online-gateway.ghn.vn",
    "timeout": 30000,
    "testMode": true
  }
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Cấu hình GHN chính",
  "type": "GHN",
  "ghnConfig": {
    "baseUrl": "https://dev-online-gateway.ghn.vn",
    "timeout": 30000,
    "testMode": true,
    "hasToken": true,
    "hasShopId": true
  },
  "createdAt": 1640995200000
}
```

### 3. Lấy danh sách cấu hình

**GET** `/v1/user/provider-shipments?page=1&limit=10&type=GHTK`

**Response:**
```json
{
  "items": [
    {
      "id": "uuid-string",
      "name": "Cấu hình GHTK chính",
      "type": "GHTK",
      "ghtkConfig": {
        "baseUrl": "https://services-staging.ghtklab.com",
        "timeout": 30000,
        "testMode": true,
        "hasToken": true
      },
      "createdAt": 1640995200000
    }
  ],
  "meta": {
    "totalItems": 1,
    "itemCount": 1,
    "itemsPerPage": 10,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

### 4. Lấy cấu hình theo ID

**GET** `/v1/user/provider-shipments/{id}`

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Cấu hình GHTK chính",
  "type": "GHTK",
  "ghtkConfig": {
    "baseUrl": "https://services-staging.ghtklab.com",
    "timeout": 30000,
    "testMode": true,
    "hasToken": true
  },
  "createdAt": 1640995200000
}
```

### 5. Cập nhật cấu hình

**PUT** `/v1/user/provider-shipments/{id}`

**Request Body:**
```json
{
  "name": "Cấu hình GHTK chính - Updated",
  "ghtkConfig": {
    "token": "new-token-here",
    "baseUrl": "https://services-staging.ghtklab.com",
    "timeout": 25000,
    "testMode": false
  }
}
```

### 6. Xóa cấu hình

**DELETE** `/v1/user/provider-shipments/{id}`

**Response:**
```json
{
  "message": "Xóa cấu hình thành công"
}
```

### 7. Lấy danh sách loại provider đã cấu hình

**GET** `/v1/user/provider-shipments/configured/types`

**Response:**
```json
{
  "types": ["GHTK", "GHN"]
}
```

### 8. Validate cấu hình GHTK

**POST** `/v1/user/provider-shipments/validate/ghtk`

**Response:**
```json
{
  "success": true,
  "message": "Cấu hình GHTK hợp lệ",
  "details": {
    "baseUrl": "https://services-staging.ghtklab.com",
    "testMode": true,
    "timeout": 30000
  }
}
```

### 9. Validate cấu hình GHN

**POST** `/v1/user/provider-shipments/validate/ghn`

**Response:**
```json
{
  "success": true,
  "message": "Cấu hình GHN hợp lệ",
  "details": {
    "baseUrl": "https://dev-online-gateway.ghn.vn",
    "testMode": true,
    "timeout": 30000,
    "shopId": "196768"
  }
}
```

### 10. Validate tất cả cấu hình

**POST** `/v1/user/provider-shipments/validate/all`

**Response:**
```json
{
  "ghtk": {
    "success": true,
    "message": "Cấu hình GHTK hợp lệ",
    "details": {
      "baseUrl": "https://services-staging.ghtklab.com",
      "testMode": true,
      "timeout": 30000
    }
  },
  "ghn": {
    "success": true,
    "message": "Cấu hình GHN hợp lệ",
    "details": {
      "baseUrl": "https://dev-online-gateway.ghn.vn",
      "testMode": true,
      "timeout": 30000,
      "shopId": "196768"
    }
  }
}
```

## Error Responses

### 400 Bad Request
```json
{
  "code": 400,
  "message": "Cấu hình GHTK là bắt buộc khi type = GHTK"
}
```

### 404 Not Found
```json
{
  "code": 404,
  "message": "Không tìm thấy cấu hình"
}
```

### 500 Internal Server Error
```json
{
  "code": 500,
  "message": "Lỗi khi tạo cấu hình: Database connection failed"
}
```

## Security Notes

1. **Mã hóa**: Tất cả thông tin nhạy cảm (token, API key) được mã hóa trước khi lưu database
2. **Không trả về thông tin nhạy cảm**: Response chỉ trả về `hasToken`, `hasShopId` thay vì token thực
3. **User isolation**: Mỗi user chỉ có thể truy cập cấu hình của chính mình
4. **JWT Authentication**: Tất cả API đều yêu cầu JWT token hợp lệ
