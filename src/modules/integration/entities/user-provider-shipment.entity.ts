import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';

/**
 * Entity đại diện cho bảng user_provider_shipments trong cơ sở dữ liệu
 * Lưu trữ thông tin tích hợp vận chuyển của người dùng với các nhà cung cấp
 */
@Entity('user_provider_shipments')
export class UserProviderShipment {
  /**
   * ID tự động tăng dạng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tên hiển thị của cấu hình tích hợp
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: true })
  name: string;

  /**
   * Dữ liệu xác thực đã được mã hóa
   * Chứa thông tin nhạy cảm như API key, token, secret key của từng nhà cung cấp
   */
  @Column({ name: 'key', type: 'text' })
  key: string;

  /**
   * Loại nhà cung cấp vận chuyển
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: ProviderShipmentType
  })
  type: ProviderShipmentType;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt: number;
}
