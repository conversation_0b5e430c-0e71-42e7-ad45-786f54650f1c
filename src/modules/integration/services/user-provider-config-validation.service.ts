import { Injectable, Logger } from '@nestjs/common';
import { UserProviderShipmentService } from './user-provider-shipment.service';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import { GHTKValidationService } from './providers/ghtk-validation.service';
import { GHNValidationService } from './providers/ghn-validation.service';

/**
 * Interface cho kết quả validation
 */
interface ValidationResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Service validation cấu hình provider của user
 */
@Injectable()
export class UserProviderConfigValidationService {
  private readonly logger = new Logger(UserProviderConfigValidationService.name);

  constructor(
    private readonly userProviderShipmentService: UserProviderShipmentService,
    private readonly ghtkValidationService: GHTKValidationService,
    private readonly ghnValidationService: GHNValidationService
  ) {}

  /**
   * Validate cấu hình GHTK của user
   */
  async validateGHTKConfig(userId: number): Promise<ValidationResult> {
    try {
      this.logger.log(`Validate cấu hình GHTK cho user ${userId}`);

      // Lấy cấu hình GHTK của user
      const config = await this.userProviderShipmentService.getDecryptedConfig(
        userId,
        ProviderShipmentType.GHTK
      );

      if (!config) {
        return {
          success: false,
          message: 'Chưa có cấu hình GHTK'
        };
      }

      // Validate với GHTK API
      const validationResult = await this.ghtkValidationService.testConnection({
        token: config.token,
        environment: config.isTestMode ? 'staging' : 'production'
      });

      return {
        success: validationResult.success,
        message: validationResult.message,
        details: {
          baseUrl: config.baseUrl,
          isTestMode: config.isTestMode,
          timeout: config.timeout
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi validate cấu hình GHTK:`, error);
      return {
        success: false,
        message: `Lỗi khi validate cấu hình GHTK: ${error.message}`
      };
    }
  }

  /**
   * Validate cấu hình GHN của user
   */
  async validateGHNConfig(userId: number): Promise<ValidationResult> {
    try {
      this.logger.log(`Validate cấu hình GHN cho user ${userId}`);

      // Lấy cấu hình GHN của user
      const config = await this.userProviderShipmentService.getDecryptedConfig(
        userId,
        ProviderShipmentType.GHN
      );

      if (!config) {
        return {
          success: false,
          message: 'Chưa có cấu hình GHN'
        };
      }

      // Validate với GHN API
      const validationResult = await this.ghnValidationService.testConnection({
        token: config.token,
        shopId: config.shopId,
        environment: config.isTestMode ? 'staging' : 'production'
      });

      return {
        success: validationResult.success,
        message: validationResult.message,
        details: {
          baseUrl: config.baseUrl,
          isTestMode: config.isTestMode,
          timeout: config.timeout,
          shopId: config.shopId
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi validate cấu hình GHN:`, error);
      return {
        success: false,
        message: `Lỗi khi validate cấu hình GHN: ${error.message}`
      };
    }
  }

  /**
   * Validate tất cả cấu hình của user
   */
  async validateAllConfigs(userId: number): Promise<{
    ghtk?: ValidationResult;
    ghn?: ValidationResult;
  }> {
    try {
      this.logger.log(`Validate tất cả cấu hình cho user ${userId}`);

      const results: any = {};

      // Validate GHTK
      try {
        results.ghtk = await this.validateGHTKConfig(userId);
      } catch (error) {
        results.ghtk = {
          success: false,
          message: `Lỗi khi validate GHTK: ${error.message}`
        };
      }

      // Validate GHN
      try {
        results.ghn = await this.validateGHNConfig(userId);
      } catch (error) {
        results.ghn = {
          success: false,
          message: `Lỗi khi validate GHN: ${error.message}`
        };
      }

      return results;
    } catch (error) {
      this.logger.error(`Lỗi khi validate tất cả cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy cấu hình đã validate cho business module sử dụng
   */
  async getValidatedConfig(userId: number, type: ProviderShipmentType): Promise<any> {
    try {
      const config = await this.userProviderShipmentService.getDecryptedConfig(userId, type);
      
      if (!config) {
        return null;
      }

      if (type === ProviderShipmentType.GHTK) {
        return {
          token: config.token,
          baseUrl: config.baseUrl,
          timeout: config.timeout,
          isTestMode: config.isTestMode
        };
      }

      if (type === ProviderShipmentType.GHN) {
        return {
          token: config.token,
          shopId: config.shopId,
          baseUrl: config.baseUrl,
          timeout: config.timeout,
          isTestMode: config.isTestMode
        };
      }

      return null;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình validated:`, error);
      return null;
    }
  }
}
