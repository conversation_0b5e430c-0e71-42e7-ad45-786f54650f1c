import { Injectable, Logger } from '@nestjs/common';
import { UserProviderShipmentService } from './user-provider-shipment.service';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import { GHTKValidationService } from './providers/ghtk-validation.service';
import { GHNValidationService } from './providers/ghn-validation.service';

/**
 * Interface cho kết quả validation
 */
interface ValidationResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Service validation cấu hình provider của user
 */
@Injectable()
export class UserProviderConfigValidationService {
  private readonly logger = new Logger(UserProviderConfigValidationService.name);

  constructor(
    private readonly userProviderShipmentService: UserProviderShipmentService,
    private readonly ghtkValidationService: GHTKValidationService,
    private readonly ghnValidationService: GHNValidationService
  ) {}

  /**
   * Validate cấu hình GHTK của user
   */
  async validateGHTKConfig(userId: number): Promise<ValidationResult> {
    try {
      this.logger.log(`Validate cấu hình GHTK cho user ${userId}`);

      // Lấy cấu hình GHTK của user
      const config = await this.userProviderShipmentService.getDecryptedConfig(
        userId, 
        ProviderShipmentType.GHTK
      );

      if (!config || !config.ghtk) {
        return {
          success: false,
          message: 'Chưa có cấu hình GHTK'
        };
      }

      // Validate với GHTK API
      const validationResult = await this.ghtkValidationService.validateCredentials({
        token: config.ghtk.token,
        baseUrl: config.ghtk.baseUrl
      });

      return {
        success: validationResult.success,
        message: validationResult.message,
        details: {
          baseUrl: config.ghtk.baseUrl,
          testMode: config.ghtk.testMode,
          timeout: config.ghtk.timeout
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi validate cấu hình GHTK:`, error);
      return {
        success: false,
        message: `Lỗi khi validate cấu hình GHTK: ${error.message}`
      };
    }
  }

  /**
   * Validate cấu hình GHN của user
   */
  async validateGHNConfig(userId: number): Promise<ValidationResult> {
    try {
      this.logger.log(`Validate cấu hình GHN cho user ${userId}`);

      // Lấy cấu hình GHN của user
      const config = await this.userProviderShipmentService.getDecryptedConfig(
        userId, 
        ProviderShipmentType.GHN
      );

      if (!config || !config.ghn) {
        return {
          success: false,
          message: 'Chưa có cấu hình GHN'
        };
      }

      // Validate với GHN API
      const validationResult = await this.ghnValidationService.validateCredentials({
        token: config.ghn.token,
        shopId: config.ghn.shopId,
        baseUrl: config.ghn.baseUrl
      });

      return {
        success: validationResult.success,
        message: validationResult.message,
        details: {
          baseUrl: config.ghn.baseUrl,
          testMode: config.ghn.testMode,
          timeout: config.ghn.timeout,
          shopId: config.ghn.shopId
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi validate cấu hình GHN:`, error);
      return {
        success: false,
        message: `Lỗi khi validate cấu hình GHN: ${error.message}`
      };
    }
  }

  /**
   * Validate tất cả cấu hình của user
   */
  async validateAllConfigs(userId: number): Promise<{
    ghtk?: ValidationResult;
    ghn?: ValidationResult;
  }> {
    try {
      this.logger.log(`Validate tất cả cấu hình cho user ${userId}`);

      const results: any = {};

      // Validate GHTK
      try {
        results.ghtk = await this.validateGHTKConfig(userId);
      } catch (error) {
        results.ghtk = {
          success: false,
          message: `Lỗi khi validate GHTK: ${error.message}`
        };
      }

      // Validate GHN
      try {
        results.ghn = await this.validateGHNConfig(userId);
      } catch (error) {
        results.ghn = {
          success: false,
          message: `Lỗi khi validate GHN: ${error.message}`
        };
      }

      return results;
    } catch (error) {
      this.logger.error(`Lỗi khi validate tất cả cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy cấu hình đã validate cho business module sử dụng
   */
  async getValidatedConfig(userId: number, type: ProviderShipmentType): Promise<any> {
    try {
      const config = await this.userProviderShipmentService.getDecryptedConfig(userId, type);
      
      if (!config) {
        return null;
      }

      if (type === ProviderShipmentType.GHTK && config.ghtk) {
        return {
          token: config.ghtk.token,
          baseUrl: config.ghtk.baseUrl,
          timeout: config.ghtk.timeout,
          testMode: config.ghtk.testMode
        };
      }

      if (type === ProviderShipmentType.GHN && config.ghn) {
        return {
          token: config.ghn.token,
          shopId: config.ghn.shopId,
          baseUrl: config.ghn.baseUrl,
          timeout: config.ghn.timeout,
          testMode: config.ghn.testMode
        };
      }

      return null;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình validated:`, error);
      return null;
    }
  }
}
