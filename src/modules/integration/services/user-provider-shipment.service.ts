import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';
import * as crypto from 'crypto';
import { UserProviderShipmentRepository } from '../repositories/user-provider-shipment.repository';
import { UserProviderShipment } from '../entities/user-provider-shipment.entity';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import {
  CreateUserProviderShipmentDto,
  UpdateUserProviderShipmentDto,
  UserProviderShipmentResponseDto,
  GHTKConfigResponseDto,
  GHNConfigResponseDto
} from '../dto/user-provider-shipment';
import { AppException } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response';

/**
 * Interface cho cấu hình đã giải mã
 */
interface DecryptedConfig {
  ghtk?: {
    token: string;
    baseUrl: string;
    timeout: number;
    testMode: boolean;
  };
  ghn?: {
    token: string;
    shopId: string;
    baseUrl: string;
    timeout: number;
    testMode: boolean;
  };
}

/**
 * Service xử lý cấu hình provider shipment của user
 */
@Injectable()
export class UserProviderShipmentService {
  private readonly logger = new Logger(UserProviderShipmentService.name);
  private readonly encryptionKey: string;

  constructor(
    private readonly repository: UserProviderShipmentRepository,
    private readonly configService: ConfigService
  ) {
    // Lấy encryption key từ environment hoặc tạo default
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY') || 'default-encryption-key-32-chars!!';
  }

  /**
   * Mã hóa dữ liệu cấu hình
   */
  private encrypt(text: string): string {
    try {
      const algorithm = 'aes-256-cbc';
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      const iv = crypto.randomBytes(16);
      
      const cipher = crypto.createCipher(algorithm, key);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Lỗi khi mã hóa dữ liệu:', error);
      throw new AppException(500, 'Lỗi mã hóa dữ liệu');
    }
  }

  /**
   * Giải mã dữ liệu cấu hình
   */
  private decrypt(encryptedText: string): string {
    try {
      const algorithm = 'aes-256-cbc';
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      
      const textParts = encryptedText.split(':');
      const iv = Buffer.from(textParts.shift()!, 'hex');
      const encryptedData = textParts.join(':');
      
      const decipher = crypto.createDecipher(algorithm, key);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('Lỗi khi giải mã dữ liệu:', error);
      throw new AppException(500, 'Lỗi giải mã dữ liệu');
    }
  }

  /**
   * Tạo cấu hình mới
   */
  async create(userId: number, dto: CreateUserProviderShipmentDto): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Tạo cấu hình ${dto.type} cho user ${userId}`);

      // Validate cấu hình theo type
      this.validateConfigByType(dto);

      // Tạo object cấu hình để mã hóa
      const configData: DecryptedConfig = {};
      
      if (dto.type === ProviderShipmentType.GHTK && dto.ghtkConfig) {
        configData.ghtk = {
          token: dto.ghtkConfig.token,
          baseUrl: dto.ghtkConfig.baseUrl || 'https://services-staging.ghtklab.com',
          timeout: dto.ghtkConfig.timeout || 30000,
          testMode: dto.ghtkConfig.testMode ?? true
        };
      }

      if (dto.type === ProviderShipmentType.GHN && dto.ghnConfig) {
        configData.ghn = {
          token: dto.ghnConfig.token,
          shopId: dto.ghnConfig.shopId,
          baseUrl: dto.ghnConfig.baseUrl || 'https://dev-online-gateway.ghn.vn',
          timeout: dto.ghnConfig.timeout || 30000,
          testMode: dto.ghnConfig.testMode ?? true
        };
      }

      // Mã hóa cấu hình
      const encryptedKey = this.encrypt(JSON.stringify(configData));

      // Tạo entity
      const entity = await this.repository.save({
        userId,
        name: dto.name || `Cấu hình ${dto.type}`,
        type: dto.type,
        key: encryptedKey
      });

      return this.transformToResponse(entity);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(500, `Lỗi khi tạo cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách cấu hình với phân trang
   */
  async findByUserId(
    userId: number,
    page: number = 1,
    limit: number = 10,
    type?: ProviderShipmentType
  ): Promise<PaginatedResult<UserProviderShipmentResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách cấu hình cho user ${userId}`);

      const result = await this.repository.findByUserIdWithPagination(userId, page, limit, type);

      return {
        items: result.items.map(item => this.transformToResponse(item)),
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình:`, error);
      throw new AppException(500, `Lỗi khi lấy danh sách cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy cấu hình theo ID
   */
  async findById(userId: number, id: string): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Lấy cấu hình ${id} cho user ${userId}`);

      const entity = await this.repository.findByIdAndUserId(id, userId);
      if (!entity) {
        throw new AppException(404, 'Không tìm thấy cấu hình');
      }

      return this.transformToResponse(entity);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(500, `Lỗi khi lấy cấu hình: ${error.message}`);
    }
  }

  /**
   * Cập nhật cấu hình
   */
  async update(
    userId: number, 
    id: string, 
    dto: UpdateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Cập nhật cấu hình ${id} cho user ${userId}`);

      const entity = await this.repository.findByIdAndUserId(id, userId);
      if (!entity) {
        throw new AppException(404, 'Không tìm thấy cấu hình');
      }

      // Giải mã cấu hình hiện tại
      const currentConfig: DecryptedConfig = JSON.parse(this.decrypt(entity.key));

      // Cập nhật cấu hình
      if (dto.ghtkConfig && entity.type === ProviderShipmentType.GHTK) {
        currentConfig.ghtk = {
          token: dto.ghtkConfig.token,
          baseUrl: dto.ghtkConfig.baseUrl || currentConfig.ghtk?.baseUrl || 'https://services-staging.ghtklab.com',
          timeout: dto.ghtkConfig.timeout || currentConfig.ghtk?.timeout || 30000,
          testMode: dto.ghtkConfig.testMode ?? currentConfig.ghtk?.testMode ?? true
        };
      }

      if (dto.ghnConfig && entity.type === ProviderShipmentType.GHN) {
        currentConfig.ghn = {
          token: dto.ghnConfig.token,
          shopId: dto.ghnConfig.shopId,
          baseUrl: dto.ghnConfig.baseUrl || currentConfig.ghn?.baseUrl || 'https://dev-online-gateway.ghn.vn',
          timeout: dto.ghnConfig.timeout || currentConfig.ghn?.timeout || 30000,
          testMode: dto.ghnConfig.testMode ?? currentConfig.ghn?.testMode ?? true
        };
      }

      // Mã hóa lại
      const encryptedKey = this.encrypt(JSON.stringify(currentConfig));

      // Cập nhật entity
      const updateData: Partial<UserProviderShipment> = {
        key: encryptedKey
      };

      if (dto.name) {
        updateData.name = dto.name;
      }

      await this.repository.update(id, updateData);

      // Lấy entity đã cập nhật
      const updatedEntity = await this.repository.findByIdAndUserId(id, userId);
      return this.transformToResponse(updatedEntity!);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(500, `Lỗi khi cập nhật cấu hình: ${error.message}`);
    }
  }

  /**
   * Xóa cấu hình
   */
  async delete(userId: number, id: string): Promise<void> {
    try {
      this.logger.log(`Xóa cấu hình ${id} cho user ${userId}`);

      const exists = await this.repository.findByIdAndUserId(id, userId);
      if (!exists) {
        throw new AppException(404, 'Không tìm thấy cấu hình');
      }

      const deleted = await this.repository.deleteByIdAndUserId(id, userId);
      if (!deleted) {
        throw new AppException(500, 'Không thể xóa cấu hình');
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(500, `Lỗi khi xóa cấu hình: ${error.message}`);
    }
  }

  /**
   * Validate cấu hình theo type
   */
  private validateConfigByType(dto: CreateUserProviderShipmentDto): void {
    if (dto.type === ProviderShipmentType.GHTK && !dto.ghtkConfig) {
      throw new AppException(400, 'Cấu hình GHTK là bắt buộc khi type = GHTK');
    }

    if (dto.type === ProviderShipmentType.GHN && !dto.ghnConfig) {
      throw new AppException(400, 'Cấu hình GHN là bắt buộc khi type = GHN');
    }

    if (dto.type === ProviderShipmentType.GHTK && dto.ghnConfig) {
      throw new AppException(400, 'Không thể có cấu hình GHN khi type = GHTK');
    }

    if (dto.type === ProviderShipmentType.GHN && dto.ghtkConfig) {
      throw new AppException(400, 'Không thể có cấu hình GHTK khi type = GHN');
    }
  }

  /**
   * Transform entity thành response DTO
   */
  private transformToResponse(entity: UserProviderShipment): UserProviderShipmentResponseDto {
    try {
      const config: DecryptedConfig = JSON.parse(this.decrypt(entity.key));

      const response = plainToClass(UserProviderShipmentResponseDto, {
        id: entity.id,
        name: entity.name,
        type: entity.type,
        createdAt: entity.createdAt
      });

      // Thêm cấu hình response (không bao gồm thông tin nhạy cảm)
      if (entity.type === ProviderShipmentType.GHTK && config.ghtk) {
        response.ghtkConfig = plainToClass(GHTKConfigResponseDto, {
          baseUrl: config.ghtk.baseUrl,
          timeout: config.ghtk.timeout,
          testMode: config.ghtk.testMode,
          hasToken: !!config.ghtk.token
        });
      }

      if (entity.type === ProviderShipmentType.GHN && config.ghn) {
        response.ghnConfig = plainToClass(GHNConfigResponseDto, {
          baseUrl: config.ghn.baseUrl,
          timeout: config.ghn.timeout,
          testMode: config.ghn.testMode,
          hasToken: !!config.ghn.token,
          hasShopId: !!config.ghn.shopId
        });
      }

      return response;
    } catch (error) {
      this.logger.error('Lỗi khi transform response:', error);
      throw new AppException(500, 'Lỗi khi xử lý dữ liệu cấu hình');
    }
  }

  /**
   * Lấy cấu hình đã giải mã cho internal use
   */
  async getDecryptedConfig(userId: number, type: ProviderShipmentType): Promise<DecryptedConfig | null> {
    try {
      const entities = await this.repository.findByTypeAndUserId(type, userId);
      if (entities.length === 0) {
        return null;
      }

      // Lấy cấu hình đầu tiên (mới nhất)
      const entity = entities[0];
      return JSON.parse(this.decrypt(entity.key));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình giải mã:`, error);
      return null;
    }
  }
}
