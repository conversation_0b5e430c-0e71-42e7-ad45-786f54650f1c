import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { GHTKConfigDto, GHNConfigDto } from './create-user-provider-shipment.dto';

/**
 * DTO cho cập nhật cấu hình provider shipment
 */
export class UpdateUserProviderShipmentDto {
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình',
    example: 'Cấu hình GHTK chính - Updated',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Cấu hình GHTK (chỉ khi type = GHTK)',
    type: GHTKConfigDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GHTKConfigDto)
  ghtkConfig?: GHTKConfigDto;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hình GHN (chỉ khi type = GHN)',
    type: GHNConfigDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GHNConfigDto)
  ghnConfig?: GHNConfigDto;
}
