import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ProviderShipmentType } from '../../constants/provider-shipment-type.enum';

/**
 * DTO cho cấu hình GHTK
 */
export class GHTKConfigDto {
  @ApiProperty({
    description: 'GHTK API Token',
    example: '8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou'
  })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({
    description: 'GHTK Base URL',
    example: 'https://services-staging.ghtklab.com',
    default: 'https://services-staging.ghtklab.com'
  })
  @IsOptional()
  @IsString()
  baseUrl?: string;

  @ApiProperty({
    description: 'GHTK Timeout (milliseconds)',
    example: 30000,
    default: 30000
  })
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'GHTK Environment Mode (true = test/staging, false = production)',
    example: true,
    default: true
  })
  @IsOptional()
  isTestMode?: boolean;
}

/**
 * DTO cho cấu hình GHN
 */
export class GHNConfigDto {
  @ApiProperty({
    description: 'GHN API Token',
    example: '42d0fc57-402d-11f0-9b81-222185cb68c8'
  })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({
    description: 'GHN Shop ID',
    example: '196768'
  })
  @IsNotEmpty()
  @IsString()
  shopId: string;

  @ApiProperty({
    description: 'GHN Base URL',
    example: 'https://dev-online-gateway.ghn.vn',
    default: 'https://dev-online-gateway.ghn.vn'
  })
  @IsOptional()
  @IsString()
  baseUrl?: string;

  @ApiProperty({
    description: 'GHN Timeout (milliseconds)',
    example: 30000,
    default: 30000
  })
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'GHN Environment Mode (true = test/dev, false = production)',
    example: true,
    default: true
  })
  @IsOptional()
  isTestMode?: boolean;
}

/**
 * DTO cho tạo cấu hình provider shipment
 */
export class CreateUserProviderShipmentDto {
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình',
    example: 'Cấu hình GHTK chính'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Loại nhà cung cấp vận chuyển',
    enum: ProviderShipmentType,
    example: ProviderShipmentType.GHTK
  })
  @IsNotEmpty()
  @IsEnum(ProviderShipmentType)
  type: ProviderShipmentType;

  @ApiProperty({
    description: 'Cấu hình GHTK (chỉ khi type = GHTK)',
    type: GHTKConfigDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GHTKConfigDto)
  ghtkConfig?: GHTKConfigDto;

  @ApiProperty({
    description: 'Cấu hình GHN (chỉ khi type = GHN)',
    type: GHNConfigDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GHNConfigDto)
  ghnConfig?: GHNConfigDto;
}
