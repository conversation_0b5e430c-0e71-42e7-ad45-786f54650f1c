import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { GenericPageSubmission } from '../entities/generic-page-submission.entity';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../exceptions/generic-page-error.code';
import { GenericPageSubmissionStatusEnum } from '../constants/generic-page.enum';

@Injectable()
export class GenericPageSubmissionRepository extends Repository<GenericPageSubmission> {
  private readonly logger = new Logger(GenericPageSubmissionRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericPageSubmission, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho GenericPageSubmission
   * @returns SelectQueryBuilder<GenericPageSubmission>
   */
  private createBaseQuery(): SelectQueryBuilder<GenericPageSubmission> {
    return this.createQueryBuilder('submission');
  }

  /**
   * Tìm dữ liệu gửi theo ID
   * @param id ID của dữ liệu gửi
   * @returns Dữ liệu gửi nếu tìm thấy
   * @throws AppException nếu không tìm thấy dữ liệu gửi
   */
  async findById(id: string): Promise<GenericPageSubmission> {
    try {
      const submission = await this.createBaseQuery()
        .where('submission.id = :id', { id })
        .getOne();

      if (!submission) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_NOT_FOUND,
          `Không tìm thấy dữ liệu gửi với ID ${id}`,
        );
      }

      return submission;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding submission by ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_NOT_FOUND,
        `Lỗi khi tìm dữ liệu gửi với ID ${id}`,
      );
    }
  }

  /**
   * Tìm tất cả dữ liệu gửi của một trang
   * @param pageId ID của trang
   * @returns Danh sách dữ liệu gửi của trang
   */
  async findByPageId(pageId: string): Promise<GenericPageSubmission[]> {
    try {
      return await this.createBaseQuery()
        .where('submission.pageId = :pageId', { pageId })
        .orderBy('submission.createdAt', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(`Error finding submissions by page ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_NOT_FOUND,
        `Lỗi khi tìm dữ liệu gửi cho trang với ID ${pageId}`,
      );
    }
  }

  /**
   * Cập nhật trạng thái của dữ liệu gửi
   * @param id ID của dữ liệu gửi
   * @param status Trạng thái mới
   * @returns Dữ liệu gửi đã cập nhật
   */
  async updateStatus(id: string, status: GenericPageSubmissionStatusEnum): Promise<GenericPageSubmission> {
    try {
      // Tìm dữ liệu gửi
      const submission = await this.findById(id);

      // Cập nhật trạng thái
      submission.status = status;
      submission.updatedAt = Date.now();

      // Lưu vào cơ sở dữ liệu
      return await this.save(submission);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating submission status: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_CREATE_ERROR,
        `Lỗi khi cập nhật trạng thái dữ liệu gửi với ID ${id}`,
      );
    }
  }
}
