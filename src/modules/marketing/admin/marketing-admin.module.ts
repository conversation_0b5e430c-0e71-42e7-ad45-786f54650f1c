import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from './entities';
import { AdminTemplateEmailRepository } from './repositories/admin-template-email.repository';
import { AdminTemplateEmailService } from './services/admin-template-email.service';
import { AdminTemplateEmailController } from './controllers/admin-template-email.controller';
import {
  AdminAudienceRepository,
  AdminAudienceCustomFieldRepository,
  AdminAudienceCustomFieldDefinitionRepository,
  AdminTagRepository,
  AdminSegmentRepository
} from './repositories';
import { AdminAudienceService } from './services/admin-audience.service';
import { AdminTagService } from './services/admin-tag.service';
import { AdminSegmentService } from './services/admin-segment.service';
import { AdminAudienceCustomFieldDefinitionService } from './services/admin-audience-custom-field-definition.service';
import { AdminAudienceController } from './controllers/admin-audience.controller';
import { AdminTagController } from './controllers/admin-tag.controller';
import { AdminSegmentController } from './controllers/admin-segment.controller';
import { AdminAudienceCustomFieldDefinitionController } from './controllers/admin-audience-custom-field-definition.controller';

/**
 * Module quản lý marketing admin
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(entities)),
  ],
  controllers: [
    AdminTemplateEmailController,
    AdminAudienceController,
    AdminTagController,
    AdminSegmentController,
    AdminAudienceCustomFieldDefinitionController,
  ],
  providers: [
    // Repositories
    AdminTemplateEmailRepository,
    AdminAudienceRepository,
    AdminAudienceCustomFieldRepository,
    AdminAudienceCustomFieldDefinitionRepository,
    AdminTagRepository,
    AdminSegmentRepository,

    // Services
    AdminTemplateEmailService,
    AdminAudienceService,
    AdminTagService,
    AdminSegmentService,
    AdminAudienceCustomFieldDefinitionService,
  ],
  exports: [
    TypeOrmModule,
    // Repositories
    AdminTemplateEmailRepository,
    AdminAudienceRepository,
    AdminAudienceCustomFieldRepository,
    AdminAudienceCustomFieldDefinitionRepository,
    AdminTagRepository,
    AdminSegmentRepository,

    // Services
    AdminTemplateEmailService,
    AdminAudienceService,
    AdminTagService,
    AdminSegmentService,
    AdminAudienceCustomFieldDefinitionService,
  ],
})
export class MarketingAdminModule {}
