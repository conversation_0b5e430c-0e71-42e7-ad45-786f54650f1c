import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { ZaloCampaignMessageContentDto, ZaloCampaignZnsContentDto } from './zalo-campaign.dto';
import { ZaloSegmentConditionDto } from './zalo-segment.dto';

/**
 * Enum cho loại sự kiện tự động hóa Zalo
 */
export enum ZaloAutomationTriggerType {
  FOLLOW = 'follow',
  UNFOLLOW = 'unfollow',
  MESSAGE = 'message',
  TAG_ADDED = 'tag_added',
  TAG_REMOVED = 'tag_removed',
}

/**
 * Enum cho loại hành động tự động hóa Zalo
 */
export enum ZaloAutomationActionType {
  SEND_MESSAGE = 'send_message',
  SEND_ZNS = 'send_zns',
  ADD_TAG = 'add_tag',
  REMOVE_TAG = 'remove_tag',
}

/**
 * Enum cho trạng thái tự động hóa Zalo
 */
export enum ZaloAutomationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * DTO cho điều kiện kích hoạt tự động hóa Zalo
 */
export class ZaloAutomationTriggerDto {
  @ApiProperty({
    description: 'Loại sự kiện kích hoạt',
    enum: ZaloAutomationTriggerType,
    example: ZaloAutomationTriggerType.FOLLOW,
  })
  @IsEnum(ZaloAutomationTriggerType)
  @IsNotEmpty()
  type: ZaloAutomationTriggerType;

  @ApiProperty({
    description: 'Điều kiện bổ sung (nếu có)',
    type: [ZaloSegmentConditionDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  conditions?: ZaloSegmentConditionDto[];
}

/**
 * DTO cho hành động tự động hóa Zalo
 */
export class ZaloAutomationActionDto {
  @ApiProperty({
    description: 'Loại hành động',
    enum: ZaloAutomationActionType,
    example: ZaloAutomationActionType.SEND_MESSAGE,
  })
  @IsEnum(ZaloAutomationActionType)
  @IsNotEmpty()
  type: ZaloAutomationActionType;

  @ApiProperty({
    description: 'Nội dung tin nhắn (chỉ dùng khi type là send_message)',
    type: ZaloCampaignMessageContentDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  messageContent?: ZaloCampaignMessageContentDto;

  @ApiProperty({
    description: 'Nội dung ZNS (chỉ dùng khi type là send_zns)',
    type: ZaloCampaignZnsContentDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  znsContent?: ZaloCampaignZnsContentDto;

  @ApiProperty({
    description: 'Tag cần thêm/xóa (chỉ dùng khi type là add_tag hoặc remove_tag)',
    example: 'vip',
    required: false,
  })
  @IsString()
  @IsOptional()
  tag?: string;
}

/**
 * DTO cho việc tạo tự động hóa Zalo
 */
export class CreateZaloAutomationDto {
  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  @IsString()
  @IsNotEmpty()
  oaId: string;

  @ApiProperty({
    description: 'Tên của tự động hóa',
    example: 'Chào mừng người theo dõi mới',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mô tả của tự động hóa',
    example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Điều kiện kích hoạt',
    type: ZaloAutomationTriggerDto,
  })
  @IsObject()
  @IsNotEmpty()
  trigger: ZaloAutomationTriggerDto;

  @ApiProperty({
    description: 'Danh sách hành động',
    type: [ZaloAutomationActionDto],
  })
  @IsArray()
  @IsNotEmpty()
  actions: ZaloAutomationActionDto[];
}

/**
 * DTO cho việc cập nhật tự động hóa Zalo
 */
export class UpdateZaloAutomationDto {
  @ApiProperty({
    description: 'Tên của tự động hóa',
    example: 'Chào mừng người theo dõi mới - Cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Mô tả của tự động hóa',
    example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới - Cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Điều kiện kích hoạt',
    type: ZaloAutomationTriggerDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  trigger?: ZaloAutomationTriggerDto;

  @ApiProperty({
    description: 'Danh sách hành động',
    type: [ZaloAutomationActionDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  actions?: ZaloAutomationActionDto[];

  @ApiProperty({
    description: 'Trạng thái tự động hóa',
    enum: ZaloAutomationStatus,
    example: ZaloAutomationStatus.ACTIVE,
    required: false,
  })
  @IsEnum(ZaloAutomationStatus)
  @IsOptional()
  status?: ZaloAutomationStatus;
}

/**
 * DTO cho phản hồi thông tin tự động hóa Zalo
 */
export class ZaloAutomationResponseDto {
  @ApiProperty({
    description: 'ID của tự động hóa',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  oaId: string;

  @ApiProperty({
    description: 'Tên của tự động hóa',
    example: 'Chào mừng người theo dõi mới',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả của tự động hóa',
    example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'Điều kiện kích hoạt',
    type: ZaloAutomationTriggerDto,
  })
  trigger: ZaloAutomationTriggerDto;

  @ApiProperty({
    description: 'Danh sách hành động',
    type: [ZaloAutomationActionDto],
  })
  actions: ZaloAutomationActionDto[];

  @ApiProperty({
    description: 'Trạng thái tự động hóa',
    enum: ZaloAutomationStatus,
    example: ZaloAutomationStatus.ACTIVE,
  })
  status: ZaloAutomationStatus;

  @ApiProperty({
    description: 'Số lần đã kích hoạt',
    example: 100,
  })
  triggerCount: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;
}

/**
 * DTO cho việc truy vấn danh sách tự động hóa Zalo
 */
export class ZaloAutomationQueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên tự động hóa',
    example: 'chào mừng',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Lọc theo loại sự kiện kích hoạt',
    enum: ZaloAutomationTriggerType,
    example: ZaloAutomationTriggerType.FOLLOW,
    required: false,
  })
  @IsEnum(ZaloAutomationTriggerType)
  @IsOptional()
  triggerType?: ZaloAutomationTriggerType;

  @ApiProperty({
    description: 'Lọc theo trạng thái tự động hóa',
    enum: ZaloAutomationStatus,
    example: ZaloAutomationStatus.ACTIVE,
    required: false,
  })
  @IsEnum(ZaloAutomationStatus)
  @IsOptional()
  status?: ZaloAutomationStatus;

  @ApiProperty({
    description: 'Số trang',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng tự động hóa trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  limit?: number = 10;
}
