import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin link giới thiệu
 */
export class AffiliateReferralLinkDto {
  /**
   * Link giới thiệu
   */
  @ApiProperty({
    description: 'Link giới thiệu',
    example: 'http://app.redai.vn?ref=123',
  })
  referralLink: string;

  /**
   * ID tài khoản affiliate
   */
  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 123,
  })
  affiliateAccountId: number;

  /**
   * Tổng số lượt click
   */
  @ApiProperty({
    description: 'Tổng số lượt click',
    example: 150,
  })
  totalClicks: number;

  /**
   * Số lượt click trong 30 ngày gần đây
   */
  @ApiProperty({
    description: 'Số lượt click trong 30 ngày gần đây',
    example: 45,
  })
  recentClicks: number;
}
