import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateOrderService } from '../services/affiliate-order.service';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateCustomerOrderRepository } from '../../repositories/affiliate-customer-order.repository';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories/point-purchase-transaction.repository';
import { UserRepository } from '@/modules/user/repositories/user.repository';
import { AffiliateOrderQueryDto } from '../dto';
import { AppException, ErrorCode } from '@common/exceptions';

describe('AffiliateOrderService', () => {
  let service: AffiliateOrderService;
  let affiliateAccountRepository: AffiliateAccountRepository;
  let affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository;
  let pointPurchaseTransactionRepository: PointPurchaseTransactionRepository;
  let userRepository: UserRepository;

  // Mock repositories
  const mockAffiliateAccountRepository = {
    findByUserId: jest.fn()
  };

  const mockAffiliateCustomerOrderRepository = {
    findWithPagination: jest.fn()
  };

  const mockPointPurchaseTransactionRepository = {
    findById: jest.fn()
  };

  const mockUserRepository = {
    findById: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AffiliateOrderService,
        { provide: AffiliateAccountRepository, useValue: mockAffiliateAccountRepository },
        { provide: AffiliateCustomerOrderRepository, useValue: mockAffiliateCustomerOrderRepository },
        { provide: PointPurchaseTransactionRepository, useValue: mockPointPurchaseTransactionRepository },
        { provide: UserRepository, useValue: mockUserRepository }
      ]
    }).compile();

    service = module.get<AffiliateOrderService>(AffiliateOrderService);
    affiliateAccountRepository = module.get<AffiliateAccountRepository>(AffiliateAccountRepository);
    affiliateCustomerOrderRepository = module.get<AffiliateCustomerOrderRepository>(AffiliateCustomerOrderRepository);
    pointPurchaseTransactionRepository = module.get<PointPurchaseTransactionRepository>(PointPurchaseTransactionRepository);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getOrders', () => {
    it('should return paginated orders when affiliate account exists', async () => {
      // Arrange
      const userId = 1;
      const queryDto = new AffiliateOrderQueryDto();
      queryDto.page = 1;
      queryDto.limit = 10;

      const mockAffiliateAccount = {
        id: 123
      };

      const mockOrder = {
        orderId: 456,
        commission: 5
      };

      const mockTransaction = {
        userId: 789,
        amount: 1500000,
        createdAt: **********,
        status: 'COMPLETED'
      };

      const mockCustomer = {
        id: 789,
        fullName: 'Trần Thị B',
        email: '<EMAIL>',
        phoneNumber: '**********'
      };

      const mockPaginatedOrders = {
        items: [mockOrder],
        meta: {
          totalItems: 78,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 8,
          currentPage: 1
        }
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockAffiliateCustomerOrderRepository.findWithPagination.mockResolvedValue(mockPaginatedOrders);
      mockPointPurchaseTransactionRepository.findById.mockResolvedValue(mockTransaction);
      mockUserRepository.findById.mockResolvedValue(mockCustomer);

      // Act
      const result = await service.getOrders(userId, queryDto);

      // Assert
      expect(affiliateAccountRepository.findByUserId).toHaveBeenCalledWith(userId);
      expect(affiliateCustomerOrderRepository.findWithPagination).toHaveBeenCalledWith(
        mockAffiliateAccount.id,
        queryDto
      );
      expect(pointPurchaseTransactionRepository.findById).toHaveBeenCalledWith(mockOrder.orderId);
      expect(userRepository.findById).toHaveBeenCalledWith(mockTransaction.userId);

      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual({
        orderId: '456',
        customer: {
          id: 789,
          fullName: 'Trần Thị B',
          email: '<EMAIL>',
          phoneNumber: '**********'
        },
        orderDate: **********,
        amount: 1500000,
        commission: 75000, // 5% of 1500000
        status: 'COMPLETED'
      });
      expect(result.meta).toEqual(mockPaginatedOrders.meta);
    });

    it('should throw an exception when affiliate account does not exist', async () => {
      // Arrange
      const userId = 1;
      const queryDto = new AffiliateOrderQueryDto();
      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getOrders(userId, queryDto)).rejects.toThrow(
        new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate')
      );
    });

    it('should filter out orders with missing transaction or customer data', async () => {
      // Arrange
      const userId = 1;
      const queryDto = new AffiliateOrderQueryDto();

      const mockAffiliateAccount = {
        id: 123
      };

      const mockOrder1 = {
        orderId: 456,
        commission: 5
      };

      const mockOrder2 = {
        orderId: 457,
        commission: 5
      };

      const mockTransaction = {
        userId: 789,
        amount: 1500000,
        createdAt: **********,
        status: 'COMPLETED'
      };

      const mockCustomer = {
        id: 789,
        fullName: 'Trần Thị B',
        email: '<EMAIL>',
        phoneNumber: '**********'
      };

      const mockPaginatedOrders = {
        items: [mockOrder1, mockOrder2],
        meta: {
          totalItems: 78,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 8,
          currentPage: 1
        }
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockAffiliateCustomerOrderRepository.findWithPagination.mockResolvedValue(mockPaginatedOrders);
      
      // First order has valid transaction and customer
      mockPointPurchaseTransactionRepository.findById.mockImplementation((orderId) => {
        if (orderId === 456) return mockTransaction;
        return null; // Second order has no transaction
      });
      
      mockUserRepository.findById.mockResolvedValue(mockCustomer);

      // Act
      const result = await service.getOrders(userId, queryDto);

      // Assert
      expect(result.items).toHaveLength(1); // Only one valid order
      expect(result.items[0].orderId).toBe('456');
    });
  });
});
