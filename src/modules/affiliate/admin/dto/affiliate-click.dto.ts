import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin lượt click
 */
export class AffiliateClickDto {
  /**
   * ID của lượt click
   */
  @ApiProperty({
    description: 'ID của lượt click',
    example: 1,
  })
  id: number;

  /**
   * ID tài khoản affiliate
   */
  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 1,
  })
  affiliateAccountId: number;

  /**
   * Tên người dùng
   */
  @ApiProperty({
    description: 'Tên người dùng',
    example: 'Nguyễn Văn A',
  })
  userName: string;

  /**
   * Email người dùng
   */
  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  userEmail: string;

  /**
   * IP của người dùng
   */
  @ApiProperty({
    description: 'IP của người dùng',
    example: '***********',
  })
  ipAddress: string;

  /**
   * User agent của người dùng
   */
  @ApiProperty({
    description: 'User agent của người dùng',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  })
  userAgent: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: **********,
  })
  createdAt: number;
}
