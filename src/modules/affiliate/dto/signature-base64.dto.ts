import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches } from 'class-validator';

export class SignatureBase64Dto {
  @ApiProperty({
    description: 'Chữ ký dưới dạng base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAA...',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^data:image\/(png|jpeg|jpg);base64,/, {
    message: 'Chữ ký phải ở định dạng base64 với tiền tố data:image/png;base64, hoặc data:image/jpeg;base64,',
  })
  signatureBase64: string;
}
