import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';

/**
 * DTO cho tham số truy vấn danh sách hợp đồng nguyên tắc (user)
 */
export class RuleContractQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái hợp đồng
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái hợp đồng',
    enum: ContractStatusEnum,
    required: false,
    example: ContractStatusEnum.APPROVED,
  })
  @IsOptional()
  @IsEnum(ContractStatusEnum)
  status?: ContractStatusEnum;

  /**
   * Lọc theo loại hợp đồng
   */
  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo loại hợp đồng',
    enum: ContractTypeEnum,
    required: false,
    example: ContractTypeEnum.INDIVIDUAL,
  })
  @IsOptional()
  @IsEnum(ContractTypeEnum)
  type?: ContractTypeEnum;
}
