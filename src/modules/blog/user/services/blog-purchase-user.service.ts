import { Injectable } from '@nestjs/common';
import { BlogPurchase } from '../../entities';
import { GetPurchaseListDto } from '@modules/blog/dto';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';
import { BlogPurchaseRepository } from '@modules/blog/repositories';
import { SqlHelper } from '@common/helpers/sql.helper';
import { AppException } from '@/common';
import { BLOG_ERROR_CODE } from '@modules/blog/exceptions';

@Injectable()
export class BlogPurchaseUserService {
  constructor(
    private readonly customBlogPurchaseRepository: BlogPurchaseRepository,
    private readonly sqlHelper: SqlHelper,
  ) {}

  /**
   * Format a CDN URL by adding the CDN_URL prefix if needed
   * @param url The URL or path to format
   * @returns Properly formatted CDN URL
   */
  private formatCdnUrl(url: string | null | undefined): string {
    if (!url) return '';

    // If the URL already starts with http, return it as is
    if (url.startsWith('http')) return url;

    // Add the CDN_URL prefix with a slash between domain and path
    const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
    return `${cdnUrl}/${url}`;
  }

  /**
   * Mua bài viết
   * @param userId ID của người dùng
   * @param blogId ID của bài viết
   * @returns Thông tin giao dịch mua bài viết
   */
  async purchaseBlog(userId: number, blogId: number): Promise<BlogPurchase> {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogs = await this.sqlHelper.select(
        'blogs',
        ['id', 'point', 'user_id as userId'],
        [
          { condition: 'id = :id', params: { id: blogId } },
          { condition: 'enable = :enable', params: { enable: true } },
          {
            condition: 'status = :status',
            params: { status: BlogStatusEnum.APPROVED },
          },
        ],
      );

      if (!blogs || blogs.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
      }

      const blog = blogs[0];

      // Kiểm tra người dùng có tồn tại không và lấy số dư điểm
      // Sử dụng truy vấn SQL trực tiếp để đảm bảo lấy đúng giá trị
      const userQuery = `
        SELECT id, points_balance
        FROM users
        WHERE id = $1 AND is_active = true
      `;

      const users = await this.customBlogPurchaseRepository.query(userQuery, [userId]);

      if (!users || users.length === 0) {
        throw new AppException(
          BLOG_ERROR_CODE.BLOG_ACCESS_DENIED,
          'User not found or not active',
        );
      }

      const user = users[0];
      console.log('User data from DB:', user);

      // Kiểm tra người dùng đã mua bài viết này chưa
      const hasPurchased = await this.sqlHelper.exists('blog_purchases', [
        { condition: 'user_id = :userId', params: { userId } },
        { condition: 'blog_id = :blogId', params: { blogId } },
      ]);

      if (hasPurchased) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_ALREADY_PURCHASED);
      }

      // Kiểm tra người dùng có đủ point không
      // Đảm bảo các giá trị point là số hợp lệ
      let userPointsBalance = 0;
      let blogPoint = 0;

      try {
        userPointsBalance = user.points_balance ? Number(user.points_balance) : 0;
        blogPoint = blog.point ? Number(blog.point) : 0;

        console.log('Debug points:', {
          userId,
          blogId,
          rawUserPoints: user.points_balance,
          rawBlogPoints: blog.point,
          userPointsBalance,
          blogPoint
        });

        if (isNaN(userPointsBalance) || isNaN(blogPoint)) {
          console.error('Invalid point values:', { userPointsBalance: user.points_balance, blogPoint: blog.point });
          throw new Error('Invalid point conversion');
        }
      } catch (err) {
        console.error('Error converting points:', err);
        throw new AppException(
          BLOG_ERROR_CODE.BLOG_PURCHASE_FAILED,
          'Invalid point values',
        );
      }

      // Chuyển đổi sang số nguyên để so sánh chính xác
      const userPointsInt = Math.floor(userPointsBalance);
      const blogPointInt = Math.floor(blogPoint);

      console.log('Comparing points:', { userPointsInt, blogPointInt, comparison: userPointsInt < blogPointInt });

      if (userPointsInt < blogPointInt) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_INSUFFICIENT_POINTS);
      }

      // Kiểm tra người dùng không mua bài viết của chính mình
      const blogUserId = Number(blog.userId);
      if (blogUserId === userId) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_CANNOT_PURCHASE_OWN);
      }

      // Phí sàn mặc định là 5%
      const platformFeePercent = 5;

      // Thực hiện giao dịch mua bài viết
      return this.customBlogPurchaseRepository.purchaseBlogWithTransaction(
        userId,
        blogId,
        blogPoint,
        platformFeePercent,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_PURCHASE_FAILED);
    }
  }

  /**
   * Kiểm tra người dùng đã mua bài viết chưa
   * @param userId ID của người dùng
   * @param blogId ID của bài viết
   * @returns Thông tin trạng thái mua
   */
  async checkPurchaseStatus(
    userId: number,
    blogId: number,
  ): Promise<{ purchased: boolean; purchased_at?: number }> {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogs = await this.sqlHelper.select(
        'blogs',
        ['id', 'user_id as userId'],
        [
          { condition: 'id = :id', params: { id: blogId } },
          { condition: 'enable = :enable', params: { enable: true } },
          {
            condition: 'status = :status',
            params: { status: BlogStatusEnum.APPROVED },
          },
        ],
      );

      if (!blogs || blogs.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
      }

      // Nếu người dùng là tác giả của bài viết, trả về đã mua
      const blogUserId = Number(blogs[0].userId);
      if (blogUserId === userId) {
        return { purchased: true };
      }

      // Kiểm tra người dùng đã mua bài viết chưa
      const purchases = await this.sqlHelper.select(
        'blog_purchases',
        ['purchased_at as purchasedAt'],
        [
          { condition: 'user_id = :userId', params: { userId } },
          { condition: 'blog_id = :blogId', params: { blogId } },
        ],
      );

      if (!purchases || purchases.length === 0) {
        return { purchased: false };
      }

      return {
        purchased: true,
        purchased_at: Number(purchases[0].purchasedAt),
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_PURCHASE_STATUS_CHECK_FAILED);
    }
  }

  /**
   * Lấy danh sách bài viết đã mua
   * @param userId ID của người dùng hiện tại
   * @param dto Tham số phân trang và lọc theo thời gian
   * @returns Danh sách bài viết đã mua có phân trang
   */
  async getPurchasedBlogs(userId: number, dto: GetPurchaseListDto) {
    try {
      // Kiểm tra người dùng có tồn tại không
      const userExists = await this.sqlHelper.exists('users', [
        { condition: 'id = :id', params: { id: userId } },
        { condition: 'is_active = :isActive', params: { isActive: true } },
      ]);

      if (!userExists) {
        throw new AppException(
          BLOG_ERROR_CODE.BLOG_ACCESS_DENIED,
          'User not found or not active',
        );
      }

      // Tính offset cho phân trang
      const offset = (dto.page - 1) * dto.limit;

      // Lấy danh sách bài viết đã mua với phân trang
      const purchases = await this.sqlHelper.select(
        'blog_purchases',
        [
          'blog_purchases.id',
          'blog_purchases.user_id as userId',
          'blog_purchases.blog_id as blogId',
          'blog_purchases.point',
          'blog_purchases.purchased_at as purchasedAt',
          'blogs.id as "blog.id"',
          'blogs.title as "blog.title"',
          'blogs.content as "blog.content"',
          'blogs.point as "blog.point"',
          'blogs.view_count as "blog.viewCount"',
          'blogs.thumbnail_url as "blog.thumbnailUrl"',
          'blogs.tags as "blog.tags"',
          'blogs.created_at as "blog.createdAt"',
          'blogs.updated_at as "blog.updatedAt"',
          'blogs.user_id as "blog.userId"',
          'blogs.employee_id as "blog.employeeId"',
          'blogs.author_type as "blog.authorType"',
          'blogs.status as "blog.status"',
          'blogs.enable as "blog.enable"',
          'blogs.like as "blog.like"',
          'users.id as "author.id"',
          'users.full_name as "author.name"',
          'users.avatar as "author.avatar"',
          'employees.id as "employee.id"',
          'employees.full_name as "employee.name"',
          'employees.avatar as "employee.avatar"',
        ],
        [
          { condition: 'blog_purchases.user_id = :userId', params: { userId } },
          // Thêm điều kiện lọc theo thời gian nếu có
          ...(dto.start_date ? [{ condition: 'blog_purchases.purchased_at >= :startDate', params: { startDate: dto.start_date } }] : []),
          ...(dto.end_date ? [{ condition: 'blog_purchases.purchased_at <= :endDate', params: { endDate: dto.end_date } }] : []),
        ],
        [
          {
            type: 'LEFT',
            table: 'blogs',
            alias: 'blogs',
            condition: 'blog_purchases.blog_id = blogs.id AND blogs.enable = true',
          },
          {
            type: 'LEFT',
            table: 'users',
            alias: 'users',
            condition: 'blogs.user_id = users.id',
          },
          {
            type: 'LEFT',
            table: 'employees',
            alias: 'employees',
            condition: 'blogs.employee_id = employees.id',
          },
        ],
        [{ column: 'blog_purchases.purchased_at', direction: 'DESC' }],
        [],
        undefined,
        { limit: dto.limit, offset: offset },
      );

      // Lấy tổng số bài viết đã mua
      const totalItems = await this.sqlHelper.count('blog_purchases', [
        { condition: 'user_id = :userId', params: { userId } },
        // Thêm điều kiện lọc theo thời gian nếu có
        ...(dto.start_date ? [{ condition: 'purchased_at >= :startDate', params: { startDate: dto.start_date } }] : []),
        ...(dto.end_date ? [{ condition: 'purchased_at <= :endDate', params: { endDate: dto.end_date } }] : []),
      ]);

      // Chuyển đổi kết quả thành định dạng mong muốn
      const purchasesWithBlogDetails = purchases.map((purchase) => {
        // Xác định loại tác giả và thông tin tác giả
        const authorType = purchase['blog.authorType'] || 'USER';
        const author = {
          id: authorType === 'USER' ? Number(purchase['author.id']) || null : Number(purchase['employee.id']) || null,
          name: authorType === 'USER' ? purchase['author.name'] || '' : purchase['employee.name'] || '',
          type: authorType,
          avatar: authorType === 'USER' ? purchase['author.avatar'] || '' : purchase['employee.avatar'] || '',
        };

        return {
          id: Number(purchase['blog.id']) || null,
          title: purchase['blog.title'] || '',
          content: this.formatCdnUrl(purchase['blog.content'] as string),
          contentUploadUrl: null, // Không cần cho bài viết đã mua
          thumbnailUploadUrl: null, // Không cần cho bài viết đã mua
          point: Number(purchase['blog.point']) || 0,
          viewCount: Number(purchase['blog.viewCount']) || 0,
          thumbnailUrl: this.formatCdnUrl(purchase['blog.thumbnailUrl'] as string),
          tags: purchase['blog.tags'] || [],
          createdAt: Number(purchase['blog.createdAt']) || null,
          updatedAt: Number(purchase['blog.updatedAt']) || null,
          userId: Number(purchase['blog.userId']) || null,
          employeeId: Number(purchase['blog.employeeId']) || null,
          authorType: purchase['blog.authorType'] || 'USER',
          author: author,
          employeeModerator: Number(purchase['blog.employeeModerator']) || null,
          status: purchase['blog.status'] || 'APPROVED',
          enable: purchase['blog.enable'] === true,
          like: Number(purchase['blog.like']) || 0,
          isPurchased: true
        };
      });

      return {
        content: purchasesWithBlogDetails,
        totalItems,
        itemCount: purchasesWithBlogDetails.length,
        itemsPerPage: dto.limit,
        totalPages: Math.ceil(totalItems / dto.limit),
        currentPage: dto.page,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_PURCHASED_LIST_FETCH_FAILED);
    }
  }

  /**
   * Lấy chi tiết giao dịch mua bài viết
   * @param userId ID của người dùng
   * @param purchaseId ID của giao dịch mua bài viết
   * @returns Chi tiết giao dịch mua bài viết
   */
  async getPurchaseDetail(userId: number, purchaseId: number) {
    try {
      // Kiểm tra người dùng có tồn tại không
      const userExists = await this.sqlHelper.exists('users', [
        { condition: 'id = :id', params: { id: userId } },
        { condition: 'is_active = :isActive', params: { isActive: true } },
      ]);

      if (!userExists) {
        throw new AppException(
          BLOG_ERROR_CODE.BLOG_ACCESS_DENIED,
          'User not found or not active',
        );
      }

      // Sử dụng truy vấn SQL trực tiếp để lấy dữ liệu
      const query = `
        SELECT
          bp.id,
          bp.user_id,
          bp.blog_id,
          bp.point,
          bp.purchased_at,
          bp.platform_fee_percent,
          bp.seller_receive_price,
          b.id as blog_id,
          b.title as blog_title,
          b.content as blog_content,
          b.point as blog_point,
          b.view_count as blog_view_count,
          b.thumbnail_url as blog_thumbnail_url,
          b.tags as blog_tags,
          b.created_at as blog_created_at,
          b.updated_at as blog_updated_at,
          b.user_id as blog_user_id,
          b.employee_id as blog_employee_id,
          b.author_type as blog_author_type,
          b.status as blog_status,
          b.enable as blog_enable,
          b.like as blog_like,
          u.id as author_user_id,
          u.full_name as author_user_name,
          u.avatar as author_user_avatar,
          e.id as author_employee_id,
          e.full_name as author_employee_name,
          e.avatar as author_employee_avatar,
          buyer.id as buyer_id,
          buyer.full_name as buyer_name,
          buyer.avatar as buyer_avatar
        FROM blog_purchases bp
        LEFT JOIN blogs b ON bp.blog_id = b.id
        LEFT JOIN users u ON b.user_id = u.id
        LEFT JOIN employees e ON b.employee_id = e.id
        LEFT JOIN users buyer ON bp.user_id = buyer.id
        WHERE bp.id = $1 AND bp.user_id = $2
      `;

      // Thực hiện truy vấn
      const result = await this.customBlogPurchaseRepository.query(query, [purchaseId, userId]);

      if (!result || result.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_PURCHASE_NOT_FOUND);
      }

      const purchase = result[0];

      // Xác định loại tác giả và thông tin tác giả
      const authorType = purchase.blog_author_type || AuthorTypeEnum.USER;
      let author;

      if (authorType === AuthorTypeEnum.USER) {
        author = {
          id: purchase.author_user_id ? Number(purchase.author_user_id) : null,
          name: purchase.author_user_name || '',
          type: AuthorTypeEnum.USER,
          avatar: purchase.author_user_avatar || '',
        };
      } else {
        author = {
          id: purchase.author_employee_id ? Number(purchase.author_employee_id) : null,
          name: purchase.author_employee_name || '',
          type: AuthorTypeEnum.SYSTEM,
          avatar: purchase.author_employee_avatar || '',
        };
      }

      // Thông tin người mua
      const buyer = {
        id: purchase.buyer_id ? Number(purchase.buyer_id) : null,
        name: purchase.buyer_name || '',
        avatar: purchase.buyer_avatar || '',
      };

      // Thông tin bài viết
      const blog = {
        id: purchase.blog_id ? Number(purchase.blog_id) : null,
        title: purchase.blog_title || '',
        content: this.formatCdnUrl(purchase.blog_content as string),
        point: purchase.blog_point ? Number(purchase.blog_point) : 0,
        viewCount: purchase.blog_view_count ? Number(purchase.blog_view_count) : 0,
        thumbnailUrl: this.formatCdnUrl(purchase.blog_thumbnail_url as string),
        tags: purchase.blog_tags || [],
        createdAt: purchase.blog_created_at ? Number(purchase.blog_created_at) : null,
        updatedAt: purchase.blog_updated_at ? Number(purchase.blog_updated_at) : null,
        userId: purchase.blog_user_id ? Number(purchase.blog_user_id) : null,
        employeeId: purchase.blog_employee_id ? Number(purchase.blog_employee_id) : null,
        authorType: purchase.blog_author_type || AuthorTypeEnum.USER,
        author: author,
        status: purchase.blog_status || BlogStatusEnum.APPROVED,
        enable: purchase.blog_enable === true,
        like: purchase.blog_like ? Number(purchase.blog_like) : 0,
      };

      // Thông tin giao dịch
      return {
        id: Number(purchase.id),
        userId: Number(purchase.user_id),
        blogId: Number(purchase.blog_id),
        point: Number(purchase.point),
        purchasedAt: Number(purchase.purchased_at),
        platformFeePercent: Number(purchase.platform_fee_percent),
        sellerReceivePrice: Number(purchase.seller_receive_price),
        blog: blog,
        buyer: buyer,
      };
    } catch (error) {
      console.error('Error in getPurchaseDetail:', error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_PURCHASE_DETAIL_FETCH_FAILED);
    }
  }
}
