import { AiProviderHelper } from '@/shared/services/ai/helpers/ai-provider.helper';
import { Injectable, Logger } from '@nestjs/common';
import { ModelBaseRepository } from '../../repositories/model-base.repository';
import { ModelFineTuneRepository } from '../../repositories/model-fine-tune.repository';
import { ModelRegistryRepository } from '../../repositories/model-registry.repository';
import { UserKeyLlmRepository } from '../../repositories/user-key-llm.repository';

/**
 * Service xử lý business logic cho User Model Base
 */
@Injectable()
export class UserModelBaseService {
  private readonly logger = new Logger(UserModelBaseService.name);

  constructor(
    private readonly modelBaseRepository: ModelBaseRepository,
    private readonly userKeyLlmRepository: UserKeyLlmRepository,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly modelFineTuneRepository: ModelFineTuneRepository,
    private readonly aiProviderHelper: AiProviderHelper,
  ) { }

  /**
   * API 1: Lấy danh sách models từ user key với pattern filtering
   * Lấy models từ API key của user và filter theo model registry patterns
   */
  // async getModelsFromUserKeysWithPatternFilter(
  //   userId: number,
  //   queryDto: UserModelBaseQueryDto
  // ): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting models from user keys with pattern filter for user ${userId}`);

  //   try {
  //     // 1. Lấy tất cả user keys của user
  //     const userKeys = await this.userKeyLlmRepository.find({
  //       where: {
  //         userId,
  //         deletedAt: IsNull(),
  //       }
  //     });
  //     if (!userKeys || userKeys.length === 0) {
  //       return ApiResponseDto.paginated({
  //         items: [],
  //         meta: {
  //           totalItems: 0,
  //           itemCount: 0,
  //           itemsPerPage: queryDto.limit || 10,
  //           totalPages: 0,
  //           currentPage: queryDto.page || 1
  //         }
  //       });
  //     }

  //     // 2. Lấy tất cả model registry patterns
  //     const modelRegistries = await this.modelRegistryRepository.find({
  //       where: { deletedAt: IsNull() }
  //     });

  //     // 3. Lấy models từ từng user key
  //     const allModels: any[] = [];

  //     for (const userKey of userKeys) {
  //       try {
  //         // Lấy models từ provider
  //         const models = await this.aiProviderHelper.getModels(
  //           userKey.provider,
  //           userKey.encryptedApiKey,
  //           1000, // Lấy nhiều models để filter
  //           false, // isAdmin = false
  //           userId
  //         );

  //         // Filter models theo patterns
  //         const filteredModels = models.filter((model: any) => {
  //           const modelName = typeof model === 'string' ? model : (model.id || model.name);

  //           // Kiểm tra model có match với pattern nào không
  //           return modelRegistries.some(registry => {
  //             if (registry.provider !== userKey.provider) return false;

  //             // Convert pattern thành regex
  //             const pattern = registry.modelNamePattern
  //               .replace(/\*/g, '.*')
  //               .replace(/\?/g, '.');
  //             const regex = new RegExp(`^${pattern}$`, 'i');

  //             return regex.test(modelName);
  //           });
  //         });

  //         // Convert sang format chuẩn
  //         filteredModels.forEach((model: any) => {
  //           const modelName = typeof model === 'string' ? model : (model.id || model.name);
  //           const modelInfo = typeof model === 'object' ? model : {};

  //           allModels.push({
  //             id: modelName,
  //             name: modelName,
  //             provider: userKey.provider,
  //             source: 'user_key',
  //             userKeyId: userKey.id,
  //             userKeyName: userKey.name,
  //             description: modelInfo.description || `Model từ ${userKey.provider}`,
  //             maxTokens: modelInfo.max_tokens || modelInfo.maxTokens,
  //             contextWindow: modelInfo.context_length || modelInfo.contextWindow,
  //             inputCostPer1kTokens: modelInfo.input_cost_per_1k_tokens,
  //             outputCostPer1kTokens: modelInfo.output_cost_per_1k_tokens,
  //             isUserAccessible: true,
  //             isFineTunable: modelInfo.fine_tunable || false,
  //             status: ModelStatusEnum.ACTIVE,
  //             metadata: {
  //               fromUserKey: true,
  //               userKeyId: userKey.id,
  //               originalModelInfo: modelInfo
  //             }
  //           });
  //         });
  //       } catch (error) {
  //         this.logger.warn(`Failed to get models from user key ${userKey.id}: ${error.message}`);
  //         // Continue với key khác
  //       }
  //     }

  //     // 4. Deduplication theo model name
  //     const uniqueModels = allModels.reduce((acc, model) => {
  //       const existing = acc.find(m => m.id === model.id && m.provider === model.provider);
  //       if (!existing) {
  //         acc.push(model);
  //       }
  //       return acc;
  //     }, []);

  //     // 5. Apply additional filters
  //     let filteredModels = uniqueModels;

  //     if (queryDto.provider) {
  //       filteredModels = filteredModels.filter((model: any) =>
  //         model.provider.toLowerCase() === queryDto.provider!.toLowerCase()
  //       );
  //     }

  //     if (queryDto.search) {
  //       const searchTerm = queryDto.search.toLowerCase();
  //       filteredModels = filteredModels.filter((model: any) =>
  //         model.name.toLowerCase().includes(searchTerm) ||
  //         model.description?.toLowerCase().includes(searchTerm)
  //       );
  //     }

  //     // 6. Sorting
  //     if (queryDto.sortBy) {
  //       const direction = queryDto.sortDirection === 'DESC' ? -1 : 1;
  //       const sortBy = queryDto.sortBy;
  //       filteredModels.sort((a: any, b: any) => {
  //         const aVal = a[sortBy] || '';
  //         const bVal = b[sortBy] || '';
  //         return aVal.toString().localeCompare(bVal.toString()) * direction;
  //       });
  //     } else {
  //       filteredModels.sort((a: any, b: any) => a.name.localeCompare(b.name));
  //     }

  //     // 7. Pagination
  //     const page = queryDto.page || 1;
  //     const limit = queryDto.limit || 10;
  //     const skip = (page - 1) * limit;
  //     const paginatedModels = filteredModels.slice(skip, skip + limit);

  //     // 8. Convert sang DTO
  //     const items = paginatedModels.map((model: any) =>
  //       UserModelBaseMapper.toResponseDto(model)
  //     );

  //     return ApiResponseDto.paginated({
  //       items,
  //       meta: {
  //         totalItems: filteredModels.length,
  //         itemCount: items.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(filteredModels.length / limit),
  //         currentPage: page
  //       }
  //     });

  //   } catch (error) {
  //     this.logger.error(`Error getting models from user keys: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }

  // /**
  //  * API 2: Lấy danh sách models từ system (admin + fine-tuning)
  //  * Lấy models từ bảng model_base và model_fine_tune
  //  */
  // async getSystemModels(
  //   userId: number,
  //   queryDto: UserModelBaseQueryDto
  // ): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting system models for user ${userId}`);

  //   try {
  //     const allModels: any[] = [];

  //     // 1. Lấy models từ bảng model_base (admin provided)
  //     const adminModels = await this.modelBaseRepository.find({
  //       where: {
  //         deletedAt: IsNull(),
  //         status: ModelStatusEnum.ACTIVE,
  //         isUserAccessible: true
  //       },
  //       order: { name: 'ASC' }
  //     });

  //     // Convert admin models sang format chuẩn
  //     adminModels.forEach(model => {
  //       allModels.push({
  //         id: model.id,
  //         name: model.name,
  //         provider: model.provider,
  //         source: 'admin_provided',
  //         description: model.description || `Model do admin cung cấp`,
  //         maxTokens: model.maxTokens,
  //         contextWindow: model.contextWindow,
  //         inputCostPer1kTokens: model.inputCostPer1kTokens,
  //         outputCostPer1kTokens: model.outputCostPer1kTokens,
  //         isUserAccessible: model.isUserAccessible,
  //         isFineTunable: model.isFineTunable,
  //         status: model.status,
  //         createdAt: model.createdAt,
  //         updatedAt: model.updatedAt,
  //         metadata: {
  //           fromAdmin: true,
  //           systemKeyLlmId: model.systemKeyLlmId,
  //           originalModelId: model.modelId
  //         }
  //       });
  //     });

  //     // 2. Lấy models từ bảng model_fine_tune (user fine-tuned models)
  //     const fineTunedModels = await this.modelFineTuneRepository.find({
  //       where: {
  //         userId,
  //         deletedAt: IsNull(),
  //         status: ModelStatusEnum.ACTIVE
  //       },
  //       order: { name: 'ASC' }
  //     });

  //     // Convert fine-tuned models sang format chuẩn
  //     fineTunedModels.forEach(model => {
  //       allModels.push({
  //         id: model.id,
  //         name: model.name,
  //         provider: model.provider,
  //         source: 'fine_tuned',
  //         description: model.description || `Model fine-tuned từ base model`,
  //         maxTokens: null, // Fine-tuned models thường inherit từ base model
  //         contextWindow: null,
  //         inputCostPer1kTokens: null, // Pricing có thể khác với base model
  //         outputCostPer1kTokens: null,
  //         isUserAccessible: true,
  //         isFineTunable: false, // Fine-tuned models thường không thể fine-tune tiếp
  //         status: model.status,
  //         createdAt: model.createdAt,
  //         updatedAt: model.createdAt, // Use createdAt since updatedAt doesn't exist
  //         metadata: {
  //           fromFineTuning: true,
  //           baseModelId: model.baseModelId,
  //           datasetId: model.datasetId,
  //           userKeyLlmId: model.userKeyLlmId,
  //           providerModelId: model.providerModelId,
  //           trainingJobId: model.trainingJobId
  //         }
  //       });
  //     });

  //     // 3. Apply filters
  //     let filteredModels = allModels;

  //     if (queryDto.provider) {
  //       filteredModels = filteredModels.filter((model: any) =>
  //         model.provider.toLowerCase() === queryDto.provider!.toLowerCase()
  //       );
  //     }

  //     if (queryDto.search) {
  //       const searchTerm = queryDto.search.toLowerCase();
  //       filteredModels = filteredModels.filter(model =>
  //         model.name.toLowerCase().includes(searchTerm) ||
  //         model.description?.toLowerCase().includes(searchTerm) ||
  //         model.source.toLowerCase().includes(searchTerm)
  //       );
  //     }

  //     if (queryDto.isFineTunable !== undefined) {
  //       filteredModels = filteredModels.filter(model =>
  //         model.isFineTunable === queryDto.isFineTunable
  //       );
  //     }

  //     // Filter theo source nếu có
  //     if (queryDto.source) {
  //       filteredModels = filteredModels.filter(model =>
  //         model.source === queryDto.source
  //       );
  //     }

  //     // 4. Sorting
  //     if (queryDto.sortBy) {
  //       const direction = queryDto.sortDirection === 'DESC' ? -1 : 1;
  //       const sortBy = queryDto.sortBy;
  //       filteredModels.sort((a: any, b: any) => {
  //         const aVal = a[sortBy] || '';
  //         const bVal = b[sortBy] || '';

  //         // Handle date sorting
  //         if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
  //           return (Number(aVal) - Number(bVal)) * direction;
  //         }

  //         return aVal.toString().localeCompare(bVal.toString()) * direction;
  //       });
  //     } else {
  //       // Default sort by name
  //       filteredModels.sort((a: any, b: any) => a.name.localeCompare(b.name));
  //     }

  //     // 5. Pagination
  //     const page = queryDto.page || 1;
  //     const limit = queryDto.limit || 10;
  //     const skip = (page - 1) * limit;
  //     const paginatedModels = filteredModels.slice(skip, skip + limit);

  //     // 6. Convert sang DTO
  //     const items = paginatedModels.map((model: any) =>
  //       UserModelBaseMapper.toResponseDto(model)
  //     );

  //     return ApiResponseDto.paginated({
  //       items,
  //       meta: {
  //         totalItems: filteredModels.length,
  //         itemCount: items.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(filteredModels.length / limit),
  //         currentPage: page
  //       }
  //     });

  //   } catch (error) {
  //     this.logger.error(`Error getting system models: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }

  // /**
  //  * Lấy danh sách model base do admin cung cấp
  //  */
  // async findAdminProvided(queryDto: UserModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log('Getting admin provided models');

  //   try {
  //     // Lấy models từ bảng model_base (admin provided only)
  //     const adminModels = await this.modelBaseRepository.find({
  //       where: {
  //         deletedAt: IsNull(),
  //         status: ModelStatusEnum.ACTIVE,
  //         isUserAccessible: true
  //       },
  //       order: { name: 'ASC' }
  //     });

  //     // Convert sang format chuẩn
  //     const allModels = adminModels.map(model => ({
  //       id: model.id,
  //       name: model.name,
  //       provider: model.provider,
  //       source: 'admin_provided',
  //       description: model.description || `Model do admin cung cấp`,
  //       maxTokens: model.maxTokens,
  //       contextWindow: model.contextWindow,
  //       inputCostPer1kTokens: model.inputCostPer1kTokens,
  //       outputCostPer1kTokens: model.outputCostPer1kTokens,
  //       isUserAccessible: model.isUserAccessible,
  //       isFineTunable: model.isFineTunable,
  //       status: model.status,
  //       createdAt: model.createdAt,
  //       updatedAt: model.updatedAt,
  //       metadata: {
  //         fromAdmin: true,
  //         systemKeyLlmId: model.systemKeyLlmId,
  //         originalModelId: model.modelId
  //       }
  //     }));

  //     // Apply filters
  //     let filteredModels = allModels;

  //     if (queryDto.provider) {
  //       filteredModels = filteredModels.filter((model: any) =>
  //         model.provider.toLowerCase() === queryDto.provider!.toLowerCase()
  //       );
  //     }

  //     if (queryDto.search) {
  //       const searchTerm = queryDto.search.toLowerCase();
  //       filteredModels = filteredModels.filter((model: any) =>
  //         model.name.toLowerCase().includes(searchTerm) ||
  //         model.description?.toLowerCase().includes(searchTerm)
  //       );
  //     }

  //     // Sorting
  //     if (queryDto.sortBy) {
  //       const direction = queryDto.sortDirection === 'DESC' ? -1 : 1;
  //       const sortBy = queryDto.sortBy;
  //       filteredModels.sort((a: any, b: any) => {
  //         const aVal = a[sortBy] || '';
  //         const bVal = b[sortBy] || '';
  //         return aVal.toString().localeCompare(bVal.toString()) * direction;
  //       });
  //     } else {
  //       filteredModels.sort((a: any, b: any) => a.name.localeCompare(b.name));
  //     }

  //     // Pagination
  //     const page = queryDto.page || 1;
  //     const limit = queryDto.limit || 10;
  //     const skip = (page - 1) * limit;
  //     const paginatedModels = filteredModels.slice(skip, skip + limit);

  //     // Convert sang DTO
  //     const items = paginatedModels.map((model: any) =>
  //       UserModelBaseMapper.toResponseDto(model)
  //     );

  //     return ApiResponseDto.paginated({
  //       items,
  //       meta: {
  //         totalItems: filteredModels.length,
  //         itemCount: items.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(filteredModels.length / limit),
  //         currentPage: page
  //       }
  //     });

  //   } catch (error) {
  //     this.logger.error(`Error getting admin provided models: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }

  // /**
  //  * Lấy danh sách model từ user key LLM
  //  */
  // async findFromUserKeys(userId: number, queryDto: UserModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting models from user keys for user ${userId}`);

  //   // Sử dụng lại logic từ getModelsFromUserKeysWithPatternFilter
  //   return this.getModelsFromUserKeysWithPatternFilter(userId, queryDto);
  // }

  // /**
  //  * Lấy danh sách model từ một user key cụ thể
  //  */
  // async findFromSpecificUserKey(userId: number, keyId: string, queryDto: UserModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting models from specific user key ${keyId} for user ${userId}`);

  //   try {
  //     // Tìm user key cụ thể
  //     const userKey = await this.userKeyLlmRepository.findOne({
  //       where: {
  //         id: keyId,
  //         userId,
  //         deletedAt: IsNull(),
  //         status: ModelStatusEnum.ACTIVE
  //       }
  //     });

  //     if (!userKey) {
  //       throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
  //     }

  //     // Lấy model registry patterns
  //     const modelRegistries = await this.modelRegistryRepository.find({
  //       where: { deletedAt: IsNull() }
  //     });

  //     // Lấy models từ user key
  //     const models = await this.aiProviderHelper.getModels(
  //       userKey.provider,
  //       userKey.encryptedApiKey,
  //       1000,
  //       false,
  //       userId
  //     );

  //     // Filter models theo patterns
  //     const filteredModels = models.filter((model: any) => {
  //       const modelName = typeof model === 'string' ? model : (model.id || model.name);

  //       return modelRegistries.some(registry => {
  //         if (registry.provider !== userKey.provider) return false;

  //         const pattern = registry.modelNamePattern
  //           .replace(/\*/g, '.*')
  //           .replace(/\?/g, '.');
  //         const regex = new RegExp(`^${pattern}$`, 'i');

  //         return regex.test(modelName);
  //       });
  //     });

  //     // Convert sang format chuẩn
  //     const allModels = filteredModels.map((model: any) => {
  //       const modelName = typeof model === 'string' ? model : (model.id || model.name);
  //       const modelInfo = typeof model === 'object' ? model : {};

  //       return {
  //         id: modelName,
  //         name: modelName,
  //         provider: userKey.provider,
  //         source: 'user_key',
  //         userKeyId: userKey.id,
  //         userKeyName: userKey.name,
  //         description: modelInfo.description || `Model từ ${userKey.provider}`,
  //         maxTokens: modelInfo.max_tokens || modelInfo.maxTokens,
  //         contextWindow: modelInfo.context_length || modelInfo.contextWindow,
  //         inputCostPer1kTokens: modelInfo.input_cost_per_1k_tokens,
  //         outputCostPer1kTokens: modelInfo.output_cost_per_1k_tokens,
  //         isUserAccessible: true,
  //         isFineTunable: modelInfo.fine_tunable || false,
  //         status: ModelStatusEnum.ACTIVE,
  //         metadata: {
  //           fromUserKey: true,
  //           userKeyId: userKey.id,
  //           originalModelInfo: modelInfo
  //         }
  //       };
  //     });

  //     // Apply additional filters
  //     let finalModels = allModels;

  //     if (queryDto.provider) {
  //       finalModels = finalModels.filter((model: any) =>
  //         model.provider.toLowerCase() === queryDto.provider!.toLowerCase()
  //       );
  //     }

  //     if (queryDto.search) {
  //       const searchTerm = queryDto.search.toLowerCase();
  //       finalModels = finalModels.filter((model: any) =>
  //         model.name.toLowerCase().includes(searchTerm) ||
  //         model.description?.toLowerCase().includes(searchTerm)
  //       );
  //     }

  //     // Sorting
  //     if (queryDto.sortBy) {
  //       const direction = queryDto.sortDirection === 'DESC' ? -1 : 1;
  //       const sortBy = queryDto.sortBy;
  //       finalModels.sort((a: any, b: any) => {
  //         const aVal = a[sortBy] || '';
  //         const bVal = b[sortBy] || '';
  //         return aVal.toString().localeCompare(bVal.toString()) * direction;
  //       });
  //     } else {
  //       finalModels.sort((a: any, b: any) => a.name.localeCompare(b.name));
  //     }

  //     // Pagination
  //     const page = queryDto.page || 1;
  //     const limit = queryDto.limit || 10;
  //     const skip = (page - 1) * limit;
  //     const paginatedModels = finalModels.slice(skip, skip + limit);

  //     // Convert sang DTO
  //     const items = paginatedModels.map((model: any) =>
  //       UserModelBaseMapper.toResponseDto(model)
  //     );

  //     return ApiResponseDto.paginated({
  //       items,
  //       meta: {
  //         totalItems: finalModels.length,
  //         itemCount: items.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(finalModels.length / limit),
  //         currentPage: page
  //       }
  //     });

  //   } catch (error) {
  //     this.logger.error(`Error getting models from specific user key: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }

  // /**
  //  * Lấy thông tin chi tiết model
  //  */
  // async getModelInfo(userId: number, modelId: string, keyId?: string): Promise<ApiResponseDto<any>> {
  //   this.logger.log(`Getting model info for ${modelId}, user ${userId}, keyId ${keyId}`);

  //   try {
  //     let modelInfo: any = null;

  //     if (keyId) {
  //       // Lấy từ user key
  //       const userKey = await this.userKeyLlmRepository.findOne({
  //         where: {
  //           id: keyId,
  //           userId,
  //           deletedAt: IsNull(),
  //           status: ModelStatusEnum.ACTIVE
  //         }
  //       });

  //       if (!userKey) {
  //         throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
  //       }

  //       // Lấy model từ provider
  //       const models = await this.aiProviderHelper.getModels(
  //         userKey.provider,
  //         userKey.encryptedApiKey,
  //         1000,
  //         false,
  //         userId
  //       );

  //       const model = models.find((m: any) => {
  //         const name = typeof m === 'string' ? m : (m.id || m.name);
  //         return name === modelId;
  //       });

  //       if (model) {
  //         modelInfo = {
  //           id: modelId,
  //           name: modelId,
  //           provider: userKey.provider,
  //           source: 'user_key',
  //           userKeyInfo: {
  //             keyId: userKey.id,
  //             keyName: userKey.name
  //           },
  //           ...(typeof model === 'object' ? model : {})
  //         };
  //       }
  //     } else {
  //       // Lấy từ admin models
  //       const adminModel = await this.modelBaseRepository.findOne({
  //         where: {
  //           id: modelId,
  //           deletedAt: IsNull(),
  //           status: ModelStatusEnum.ACTIVE,
  //           isUserAccessible: true
  //         }
  //       });

  //       if (adminModel) {
  //         modelInfo = {
  //           id: adminModel.id,
  //           name: adminModel.name,
  //           modelId: adminModel.modelId,
  //           provider: adminModel.provider,
  //           description: adminModel.description,
  //           source: 'admin',
  //           maxTokens: adminModel.maxTokens,
  //           contextWindow: adminModel.contextWindow,
  //           inputCostPer1kTokens: adminModel.inputCostPer1kTokens,
  //           outputCostPer1kTokens: adminModel.outputCostPer1kTokens,
  //           isFineTunable: adminModel.isFineTunable,
  //           metadata: adminModel.metadata
  //         };
  //       }
  //     }

  //     if (!modelInfo) {
  //       throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NOT_FOUND);
  //     }

  //     return ApiResponseDto.success(modelInfo);

  //   } catch (error) {
  //     this.logger.error(`Error getting model info: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }

  // /**
  //  * Lấy tất cả models (admin + user keys)
  //  */
  // async findAllModels(userId: number, queryDto: UserModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting all models for user ${userId}`);

  //   try {
  //     // Lấy admin models
  //     const adminResponse = await this.findAdminProvided(queryDto);
  //     const adminModels = adminResponse.result?.items || [];

  //     // Lấy user key models
  //     const userKeyResponse = await this.getModelsFromUserKeysWithPatternFilter(userId, queryDto);
  //     const userKeyModels = userKeyResponse.result?.items || [];

  //     // Merge và deduplicate
  //     const allModels = [...adminModels, ...userKeyModels];
  //     const uniqueModels = allModels.reduce((acc: any[], model) => {
  //       const existing = acc.find(m =>
  //         m.modelId === model.modelId && m.provider === model.provider
  //       );
  //       if (!existing) {
  //         acc.push(model);
  //       }
  //       return acc;
  //     }, []);

  //     // Apply additional filters nếu cần
  //     let filteredModels = uniqueModels;

  //     if (queryDto.search) {
  //       const searchTerm = queryDto.search.toLowerCase();
  //       filteredModels = filteredModels.filter((model: any) =>
  //         model.name.toLowerCase().includes(searchTerm) ||
  //         model.description?.toLowerCase().includes(searchTerm)
  //       );
  //     }

  //     // Sorting
  //     if (queryDto.sortBy) {
  //       const direction = queryDto.sortDirection === 'DESC' ? -1 : 1;
  //       const sortBy = queryDto.sortBy;
  //       filteredModels.sort((a: any, b: any) => {
  //         const aVal = a[sortBy] || '';
  //         const bVal = b[sortBy] || '';
  //         return aVal.toString().localeCompare(bVal.toString()) * direction;
  //       });
  //     } else {
  //       filteredModels.sort((a: any, b: any) => a.name.localeCompare(b.name));
  //     }

  //     // Pagination
  //     const page = queryDto.page || 1;
  //     const limit = queryDto.limit || 10;
  //     const skip = (page - 1) * limit;
  //     const paginatedModels = filteredModels.slice(skip, skip + limit);

  //     return ApiResponseDto.paginated({
  //       items: paginatedModels,
  //       meta: {
  //         totalItems: filteredModels.length,
  //         itemCount: paginatedModels.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(filteredModels.length / limit),
  //         currentPage: page
  //       }
  //     });

  //   } catch (error) {
  //     this.logger.error(`Error getting all models: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }

  // /**
  //  * Lấy models theo provider
  //  */
  // async getModelsByProvider(userId: number, provider: string, queryDto: UserModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting models by provider ${provider} for user ${userId}`);

  //   // Tạo query với provider filter
  //   const providerQueryDto = { ...queryDto, provider: provider as any };

  //   // Sử dụng findAllModels với provider filter
  //   return this.findAllModels(userId, providerQueryDto);
  // }

  // /**
  //  * Lấy models có thể fine-tune
  //  */
  // async getFineTunableModels(userId: number, queryDto: UserModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<UserModelBaseResponseDto>>> {
  //   this.logger.log(`Getting fine-tunable models for user ${userId}`);

  //   // Tạo query với isFineTunable filter
  //   const fineTunableQueryDto = { ...queryDto, isFineTunable: true };

  //   // Sử dụng findAllModels với fine-tunable filter
  //   return this.findAllModels(userId, fineTunableQueryDto);
  // }

  // /**
  //  * Tính toán chi phí ước tính cho model
  //  */
  // async calculateModelCost(
  //   userId: number,
  //   modelId: string,
  //   inputTokens: number,
  //   outputTokens: number,
  //   keyId?: string
  // ): Promise<ApiResponseDto<any>> {
  //   this.logger.log(`Calculating cost for model ${modelId}, user ${userId}`);

  //   try {
  //     // Lấy thông tin model
  //     const modelInfoResponse = await this.getModelInfo(userId, modelId, keyId);
  //     const modelInfo = modelInfoResponse.result;

  //     if (!modelInfo) {
  //       throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NOT_FOUND);
  //     }

  //     // Tính toán chi phí
  //     const inputCost = (modelInfo.inputCostPer1kTokens || 0) * (inputTokens / 1000);
  //     const outputCost = (modelInfo.outputCostPer1kTokens || 0) * (outputTokens / 1000);
  //     const totalCost = inputCost + outputCost;

  //     const costCalculation = {
  //       modelId,
  //       modelName: modelInfo.name,
  //       provider: modelInfo.provider,
  //       inputTokens,
  //       outputTokens,
  //       totalTokens: inputTokens + outputTokens,
  //       costs: {
  //         inputCost: Number(inputCost.toFixed(6)),
  //         outputCost: Number(outputCost.toFixed(6)),
  //         totalCost: Number(totalCost.toFixed(6))
  //       },
  //       pricing: {
  //         inputCostPer1kTokens: modelInfo.inputCostPer1kTokens || 0,
  //         outputCostPer1kTokens: modelInfo.outputCostPer1kTokens || 0
  //       },
  //       calculatedAt: Date.now()
  //     };

  //     return ApiResponseDto.success(costCalculation);

  //   } catch (error) {
  //     this.logger.error(`Error calculating model cost: ${error.message}`, error.stack);
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_PROVIDER_ERROR, error.message);
  //   }
  // }
}