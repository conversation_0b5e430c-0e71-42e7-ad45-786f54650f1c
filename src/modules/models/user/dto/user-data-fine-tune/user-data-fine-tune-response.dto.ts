import { DataFineTuneStatus } from '@/modules/models/constants/data-fine-tune-status.enum';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của user data fine-tune
 */
export class UserDataFineTuneResponseDto {
  @ApiProperty({
    description: 'ID của bộ dữ liệu',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Tên bộ dữ liệu',
    example: 'Customer Service Training Dataset'
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả về bộ dữ liệu',
    example: 'Bộ dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng',
    nullable: true
  })
  description: string | null;

  @ApiProperty({
    description: 'Thời gian tạo (epoch timestamp)',
    example: 1703980800000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Trạng thái của bộ dữ liệu',
    enum: DataFineTuneStatus,
    example: DataFineTuneStatus.PENDING,
  })
  status: DataFineTuneStatus;
}


export class UserDataFineTuneDetialResponseDto extends UserDataFineTuneResponseDto {
  @ApiProperty({
    description: 'Đường dẫn đến tập dữ liệu huấn luyện',
    example: 'https://s3.amazonaws.com/bucket/path/to/dataset.jsonl'
  })
  trainDatasetUrl: string;

  @ApiProperty({
    description: 'Số lượng mẫu validation',
    example: 'https://s3.amazonaws.com/bucket/path/to/dataset.jsonl',
    nullable: true
  })
  validDatasetUrl: string | null;
}
