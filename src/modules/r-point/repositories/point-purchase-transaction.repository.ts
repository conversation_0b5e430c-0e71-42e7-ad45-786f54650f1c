import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PointPurchaseTransaction } from '@modules/r-point/entities';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho PointPurchaseTransaction
 */
@Injectable()
export class PointPurchaseTransactionRepository {
  constructor(
    @InjectRepository(PointPurchaseTransaction)
    private readonly repository: Repository<PointPurchaseTransaction>
  ) {}

  /**
   * Tìm giao dịch mua điểm theo ID
   * @param id ID của giao dịch
   * @returns Thông tin giao dịch hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<PointPurchaseTransaction | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm giao dịch mua điểm theo mã tham chiếu
   * @param referenceId Mã tham chiếu từ cổng thanh toán
   * @returns Thông tin giao dịch hoặc null nếu không tìm thấy
   */
  async findByReferenceId(referenceId: string): Promise<PointPurchaseTransaction | null> {
    return this.repository.findOne({ where: { referenceId } });
  }

  /**
   * Tìm danh sách giao dịch mua điểm theo ID người dùng
   * @param userId ID của người dùng
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @returns Danh sách giao dịch với phân trang
   */
  async findByUserId(userId: number, page = 1, limit = 10): Promise<PaginatedResult<PointPurchaseTransaction>> {
    const skip = (page - 1) * limit;

    const [items, totalItems] = await this.repository.findAndCount({
      where: { userId },
      order: { createdAt: 'DESC' },
      skip,
      take: limit
    });

    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page
      }
    };
  }

  /**
   * Tìm danh sách giao dịch mua điểm theo ID tài khoản affiliate
   *
   * Lưu ý: Phương thức này cần được cập nhật khi có thêm trường affiliateAccountId trong bảng point_purchase_transactions
   * Hiện tại, phương thức này trả về danh sách rỗng vì không có cách để liên kết trực tiếp giao dịch với tài khoản affiliate
   *
   * @param affiliateAccountId ID tài khoản affiliate
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @returns Danh sách giao dịch với phân trang
   */
  async findByAffiliateAccountId(_affiliateAccountId: number, page = 1, limit = 10): Promise<PaginatedResult<PointPurchaseTransaction>> {
    // TODO: Cập nhật phương thức này khi có thêm trường affiliateAccountId trong bảng point_purchase_transactions
    // Hiện tại, không có cách để liên kết trực tiếp giao dịch với tài khoản affiliate

    // Trả về danh sách rỗng
    return {
      items: [],
      meta: {
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: limit,
        totalPages: 0,
        currentPage: page
      }
    };
  }

  /**
   * Tạo giao dịch mua điểm mới
   * @param transactionData Dữ liệu giao dịch
   * @returns Thông tin giao dịch đã tạo
   */
  async create(transactionData: Partial<PointPurchaseTransaction>): Promise<PointPurchaseTransaction> {
    const newTransaction = this.repository.create(transactionData);
    return this.repository.save(newTransaction);
  }

  /**
   * Cập nhật thông tin giao dịch
   * @param id ID của giao dịch
   * @param transactionData Dữ liệu cần cập nhật
   * @returns Kết quả cập nhật
   */
  async update(id: number, transactionData: Partial<PointPurchaseTransaction>): Promise<void> {
    await this.repository.update(id, transactionData);
  }

  /**
   * Đếm số lượng giao dịch theo điều kiện
   * @param options Điều kiện đếm
   * @returns Số lượng giao dịch
   */
  async count(options?: any): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Tìm danh sách giao dịch theo điều kiện
   * @param options Điều kiện tìm kiếm
   * @returns Danh sách giao dịch
   */
  async find(options?: any): Promise<PointPurchaseTransaction[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm một giao dịch theo điều kiện
   * @param options Điều kiện tìm kiếm
   * @returns Thông tin giao dịch hoặc null nếu không tìm thấy
   */
  async findOne(options?: any): Promise<PointPurchaseTransaction | null> {
    return this.repository.findOne(options);
  }
}
