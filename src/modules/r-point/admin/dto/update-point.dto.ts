import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

/**
 * DTO cho việc cập nhật gói point
 */
export class UpdatePointDto {
  /**
   * Tên của gói point
   * Ví dụ: "Gói 100k", "Gói 500k"
   */
  @ApiProperty({
    description: 'Tên của gói point',
    example: 'Gói 100k',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên gói point phải là chuỗi' })
  name?: string;

  /**
   * Số tiền của gói point (chỉ áp dụng cho gói không phải customize)
   * Đơn vị: VND
   */
  @ApiProperty({
    description: 'Số tiền của gói point (VND)',
    example: 100000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '<PERSON>ố tiền phải là số' })
  @Min(20000, { message: 'Số tiền phải lớn hơn hoặc bằng 20,000 VND' })
  @Max(500000, { message: 'Số tiền phải nhỏ hơn hoặc bằng 500,000 VND' })
  cash?: number;

  /**
   * Tỷ lệ quy đổi giữa tiền và point
   * Ví dụ: rate = 1000 nghĩa là 1000 VND = 1 point
   * Chỉ áp dụng khi chỉnh sửa gói CUSTOMIZE
   */
  @ApiProperty({
    description: 'Tỷ lệ quy đổi giữa tiền và point (chỉ dùng cho gói CUSTOMIZE)',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Tỷ lệ quy đổi phải là số' })
  @Min(1, { message: 'Tỷ lệ quy đổi phải lớn hơn 0' })
  rate?: number;

  /**
   * Số tiền tối thiểu cho gói customize
   * Chỉ áp dụng khi chỉnh sửa gói CUSTOMIZE
   */
  @ApiProperty({
    description: 'Số tiền tối thiểu cho gói customize (VND)',
    example: 50000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số tiền tối thiểu phải là số' })
  @Min(20000, { message: 'Số tiền tối thiểu phải lớn hơn hoặc bằng 20,000 VND' })
  min?: number;

  /**
   * Số tiền tối đa cho gói customize
   * Chỉ áp dụng khi chỉnh sửa gói CUSTOMIZE
   */
  @ApiProperty({
    description: 'Số tiền tối đa cho gói customize (VND)',
    example: 500000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số tiền tối đa phải là số' })
  @Max(500000, { message: 'Số tiền tối đa phải nhỏ hơn hoặc bằng 500,000 VND' })
  max?: number;

  /**
   * Flag xác định đây có phải là gói customize hay không
   * true: Gói customize - người dùng có thể chọn số tiền trong khoảng min-max
   * false: Gói cố định - người dùng chỉ có thể mua với số tiền cố định
   */
  @ApiProperty({
    description: 'Có phải là gói customize hay không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường isCustomize phải là boolean' })
  isCustomize?: boolean;

  /**
   * Mô tả chi tiết về gói point
   * Có thể chứa thông tin về ưu đãi, điều kiện sử dụng,...
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về gói point',
    example: 'Gói point cơ bản với tỷ lệ quy đổi 1:1000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;
}
