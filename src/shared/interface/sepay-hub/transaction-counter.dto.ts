/**
 * DTO đại diện cho bộ đếm giao dịch từ SePay Hub
 */
export interface TransactionCounterDto {
  /**
   * ID công ty (tổ chức)
   */
  company_id: string;

  /**
   * Ng<PERSON><PERSON> phát sinh bộ đếm (định dạng Y-m-d)
   */
  date: string;

  /**
   * Tổng số lượng giao dịch trong ngày
   */
  transaction: string;

  /**
   * Tổng số lượng giao dịch tiền vào trong ngày
   */
  transaction_in: string;

  /**
   * Tổng số lượng giao dịch tiền ra trong ngày
   */
  transaction_out: string;
}