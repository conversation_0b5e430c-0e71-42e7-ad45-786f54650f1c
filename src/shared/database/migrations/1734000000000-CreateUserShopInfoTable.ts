import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateUserShopInfoTable1734000000000 implements MigrationInterface {
  name = 'CreateUserShopInfoTable1734000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo bảng user_shop_info
    await queryRunner.createTable(
      new Table({
        name: 'user_shop_info',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
            comment: 'ID của thông tin shop'
          },
          {
            name: 'user_id',
            type: 'integer',
            isNullable: false,
            comment: 'ID người dùng sở hữu shop'
          },
          {
            name: 'shop_name',
            type: 'varchar',
            length: '255',
            isNullable: false,
            comment: 'Tên cửa hàng'
          },
          {
            name: 'shop_phone',
            type: 'varchar',
            length: '20',
            isNullable: false,
            comment: '<PERSON><PERSON> điện tho<PERSON>i cửa hàng'
          },
          {
            name: 'shop_address',
            type: 'varchar',
            length: '500',
            isNullable: false,
            comment: 'Đ<PERSON>a chỉ cửa hàng'
          },
          {
            name: 'shop_province',
            type: 'varchar',
            length: '100',
            isNullable: false,
            comment: 'Tỉnh/Thành phố'
          },
          {
            name: 'shop_district',
            type: 'varchar',
            length: '100',
            isNullable: false,
            comment: 'Quận/Huyện'
          },
          {
            name: 'shop_ward',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: 'Phường/Xã'
          },
          {
            name: 'created_at',
            type: 'bigint',
            isNullable: false,
            default: '(EXTRACT(epoch FROM now()) * 1000)::bigint',
            comment: 'Thời gian tạo (millis)'
          },
          {
            name: 'updated_at',
            type: 'bigint',
            isNullable: false,
            default: '(EXTRACT(epoch FROM now()) * 1000)::bigint',
            comment: 'Thời gian cập nhật (millis)'
          }
        ],
        indices: [
          new Index('IDX_user_shop_info_user_id', ['user_id']),
        ],
        uniques: [
          {
            name: 'UQ_user_shop_info_user_id',
            columnNames: ['user_id']
          }
        ]
      }),
      true
    );

    console.log('✅ Created user_shop_info table successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa bảng user_shop_info
    await queryRunner.dropTable('user_shop_info');
    console.log('✅ Dropped user_shop_info table successfully');
  }
}
