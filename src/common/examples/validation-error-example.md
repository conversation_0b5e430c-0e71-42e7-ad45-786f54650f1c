# Validation Error Handling Examples

## Cải tiến CustomValidationPipe

CustomValidationPipe đã được cải tiến để cung cấp thông tin lỗi validation chi tiết và dễ hiểu hơn.

## Ví dụ Response Lỗi Validation Mới

### 1. Lỗi Validation Đơn Giản

**Request:**
```json
{
  "name": "",
  "price": "invalid_number"
}
```

**Response:**
```json
{
  "code": 9001,
  "message": "name (không được để trống), price (phải là số)",
  "detail": {
    "summary": "name (không được để trống), price (phải là số)",
    "details": [
      {
        "field": "name",
        "value": "",
        "type": "string",
        "constraints": [
          {
            "constraint": "isNotEmpty",
            "message": "name should not be empty"
          }
        ],
        "messages": ["name should not be empty"]
      },
      {
        "field": "price",
        "value": "invalid_number",
        "type": "string",
        "constraints": [
          {
            "constraint": "isNumber",
            "message": "price must be a number"
          }
        ],
        "messages": ["price must be a number"]
      }
    ],
    "totalErrors": 2,
    "hasNestedErrors": false,
    "errorType": "multiple"
  },
  "path": "/api/v1/user/products/66",
  "requestId": "e29f1504-43d2-4edf-9a2b-73ad55f20f10"
}
```

### 2. Lỗi Validation Nested Object

**Request:**
```json
{
  "name": "Product Name",
  "classifications": [
    {
      "type": "",
      "price": {
        "listPrice": "invalid",
        "salePrice": -100,
        "currency": ""
      }
    }
  ],
  "customFields": [
    {
      "customFieldId": "not_a_number",
      "value": {
        "value": ""
      }
    }
  ]
}
```

**Response:**
```json
{
  "code": 9003,
  "message": "classifications (3 lỗi), customFields (1 lỗi)",
  "detail": {
    "summary": "classifications (3 lỗi), customFields (1 lỗi)",
    "details": [
      {
        "field": "classifications.0.type",
        "value": "",
        "type": "string",
        "constraints": [
          {
            "constraint": "isNotEmpty",
            "message": "type should not be empty"
          }
        ],
        "messages": ["type should not be empty"]
      },
      {
        "field": "classifications.0.price.listPrice",
        "value": "invalid",
        "type": "string",
        "constraints": [
          {
            "constraint": "isNumber",
            "message": "listPrice must be a number"
          }
        ],
        "messages": ["listPrice must be a number"]
      },
      {
        "field": "classifications.0.price.salePrice",
        "value": -100,
        "type": "number",
        "constraints": [
          {
            "constraint": "min",
            "message": "salePrice must not be less than 0"
          }
        ],
        "messages": ["salePrice must not be less than 0"]
      },
      {
        "field": "customFields.0.customFieldId",
        "value": "not_a_number",
        "type": "string",
        "constraints": [
          {
            "constraint": "isNumber",
            "message": "customFieldId must be a number"
          }
        ],
        "messages": ["customFieldId must be a number"]
      }
    ],
    "totalErrors": 4,
    "hasNestedErrors": true,
    "errorType": "nested"
  },
  "path": "/api/v1/user/products/66",
  "requestId": "e29f1504-43d2-4edf-9a2b-73ad55f20f10"
}
```

## Các Mã Lỗi Validation Mới

| Mã Lỗi | Tên | Mô Tả | Khi Nào Sử Dụng |
|---------|-----|-------|------------------|
| 9001 | GENERAL_VALIDATION_ERROR | Dữ liệu đầu vào không hợp lệ | Lỗi validation đơn giản, 1 field |
| 9002 | FIELD_VALIDATION_ERROR | Một hoặc nhiều trường dữ liệu không hợp lệ | Nhiều field bị lỗi, không có nested |
| 9003 | NESTED_VALIDATION_ERROR | Dữ liệu nested object không hợp lệ | Có lỗi trong nested object/array |

## Lợi Ích Của Cải Tiến

### 1. **Thông Tin Chi Tiết Hơn**
- Hiển thị chính xác field nào bị lỗi
- Giá trị hiện tại và kiểu dữ liệu
- Danh sách tất cả constraints bị vi phạm

### 2. **Thông Báo Tiếng Việt**
- Tự động dịch thông báo lỗi sang tiếng Việt
- Dễ hiểu cho developer Việt Nam

### 3. **Phân Loại Lỗi Thông Minh**
- Tự động phân biệt lỗi đơn giản, nhiều field, và nested
- Mã lỗi riêng biệt cho từng loại

### 4. **Tóm Tắt Thông Minh**
- Nhóm lỗi theo field chính
- Giới hạn số lỗi hiển thị để tránh spam
- Ưu tiên hiển thị lỗi quan trọng nhất

## Cách Debug Lỗi Validation

1. **Kiểm tra `errorType`** để biết loại lỗi
2. **Xem `summary`** để hiểu nhanh vấn đề
3. **Duyệt `details`** để biết chi tiết từng lỗi
4. **Kiểm tra `field`** để biết chính xác vị trí lỗi trong nested object

## Console Log Debug

Khi có lỗi validation, console sẽ hiển thị:

```
=== VALIDATION ERROR DETAILS ===
Error Code: 9003
Error Summary: classifications (3 lỗi), customFields (1 lỗi)
Detailed Errors: [...]
Has Nested Errors: true
Total Errors: 4
================================
```
