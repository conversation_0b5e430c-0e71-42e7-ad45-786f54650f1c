import { HttpStatus } from '@nestjs/common';
import { ApiProperty, getSchemaPath } from '@nestjs/swagger';

/**
 * Lớp định nghĩa cấu trúc chuẩn cho API Response
 * Sử dụng generic type T để định nghĩa kiểu dữ liệu của result
 * Được sử dụng cho tất cả các endpoint API trong hệ thống
 */
export class ApiResponseDto<T> {
  /**
   * Mã trạng thái
   * - 0: Thành công
   * - Khác 0: Mã lỗi cụ thể
   */
  @ApiProperty({
    description: 'Mã trạng thái (0: Thành công, khác 0: Mã lỗi cụ thể)',
    example: 0,
    type: Number,
  })
  public code: number;

  /**
   * Thông điệp mô tả kết quả
   */
  @ApiProperty({
    description: 'Thông điệp mô tả kết quả',
    example: 'Success',
    type: String,
  })
  public message?: string;

  /**
   * Dữ liệu trả về
   */
  public result?: T;

  /**
   * Khởi tạo đối tượng API Response
   * @param result Dữ liệu trả về
   * @param message Thông điệp mô tả kết quả
   * @param code Mã trạng thái HTTP
   */
  constructor(
    result: T,
    message: string = 'Success',
    code: number = HttpStatus.OK,
  ) {
    this.code = code;
    this.message = message;
    this.result = result;
  }

  // === Các phương thức tĩnh (Static factory methods) để tạo response chuẩn hóa ===

  /**
   * Tạo response thành công chuẩn (200 OK)
   * @param data Dữ liệu trả về
   * @param message Thông điệp (mặc định 'Success')
   * @returns Promise trả về ApiResponseDto với code = 0, status = 200
   */
  static success<T>(data: T, message: string = 'Success'): ApiResponseDto<T> {
    return new ApiResponseDto<T>(data, message, HttpStatus.OK);
  }

  /**
   * Tạo response thành công với mã Created (201)
   * @param data Dữ liệu đã tạo
   * @param message Thông điệp (mặc định 'Created Successfully')
   * @returns Promise trả về ApiResponseDto với code = 0, status = 201
   */
  static created<T>(
    data: T,
    message: string = 'Created Successfully',
  ): ApiResponseDto<T> {
    return new ApiResponseDto<T>(data, message, HttpStatus.CREATED);
  }

  /**
   * Tạo response cập nhật thành công (200 OK)
   * @param data Dữ liệu đã được cập nhật
   * @param message Thông điệp (mặc định 'Updated Successfully')
   * @returns Promise trả về ApiResponseDto với code = 0, status = 200
   */
  static updated<T>(
    data: T,
    message: string = 'Updated Successfully',
  ): ApiResponseDto<T> {
    return new ApiResponseDto<T>(data, message, HttpStatus.OK);
  }

  /**
   * Tạo response xóa thành công (200 OK hoặc 204 No Content)
   * @param data Dữ liệu đã xóa (nếu có)
   * @param message Thông điệp (mặc định 'Deleted Successfully')
   * @param useNoContent Nếu true, sử dụng status 204 thay vì 200
   * @returns Promise trả về ApiResponseDto với code = 0, status = 200 hoặc 204
   */
  static deleted<T = null>(
    data: T = null as T,
    message: string = 'Deleted Successfully',
    useNoContent: boolean = false,
  ): ApiResponseDto<T> {
    const status = useNoContent ? HttpStatus.NO_CONTENT : HttpStatus.OK;
    return new ApiResponseDto<T>(data, message, status);
  }

  /**
   * Tạo response thành công với dữ liệu phân trang (200 OK)
   * @param result Kết quả phân trang cần trả về
   * @param message Thông điệp (mặc định 'Success')
   * @returns Promise trả về ApiResponseDto với code = 0, status = 200 và result là đối tượng phân trang
   */
  static paginated<T>(
    result: PaginatedResult<T>,
    message: string = 'Success',
  ): ApiResponseDto<PaginatedResult<T>> {
    return new ApiResponseDto<PaginatedResult<T>>(
      result,
      message,
      HttpStatus.OK,
    );
  }

  /**
   * Tạo response không có nội dung (204 No Content)
   * @param message Thông điệp (mặc định 'No Content')
   * @returns Promise trả về ApiResponseDto với code = 0, status = 204
   */
  static noContent(message: string = 'No Content'): ApiResponseDto<null> {
    return new ApiResponseDto<null>(null, message, HttpStatus.NO_CONTENT);
  }

  /**
   * Tạo schema cho Swagger để hiển thị đúng cấu trúc response
   * @param dataDto Kiểu dữ liệu của result (class có sử dụng @ApiProperty)
   * @returns Schema cho Swagger để sử dụng trong @ApiResponse
   */
  static getSchema(dataDto: any): Record<string, any> {
    return {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(dataDto) },
          },
        },
      ],
    };
  }

  /**
   * Tạo schema cho Swagger với dữ liệu phân trang
   * @param dataDto Kiểu dữ liệu của items trong result (class có sử dụng @ApiProperty)
   * @returns Schema cho Swagger để sử dụng trong @ApiResponse cho dữ liệu phân trang
   */
  static getPaginatedSchema(dataDto: any): Record<string, any> {
    return {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(dataDto) },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    };
  }

  /**
   * Tạo schema cho Swagger với dữ liệu là mảng
   * @param dataDto Kiểu dữ liệu của các phần tử trong mảng (class có sử dụng @ApiProperty)
   * @returns Schema cho Swagger để sử dụng trong @ApiResponse cho dữ liệu dạng mảng
   */
  static getArraySchema(dataDto: any): Record<string, any> {
    return {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(dataDto) },
            },
          },
        },
      ],
    };
  }
}

/**
 * Lớp định nghĩa cấu trúc metadata của kết quả phân trang
 * Chứa các thông tin về tổng số bản ghi, số trang, và vị trí trang hiện tại
 */
export class PaginationMeta {
  /**
   * Tổng số bản ghi
   */
  @ApiProperty({
    description: 'Tổng số bản ghi',
    example: 100,
    type: Number,
  })
  totalItems: number;

  /**
   * Số lượng bản ghi trên trang hiện tại
   */
  @ApiProperty({
    description: 'Số lượng bản ghi trên trang hiện tại',
    example: 10,
    type: Number,
  })
  itemCount: number;

  /**
   * Số lượng bản ghi trên mỗi trang
   */
  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    type: Number,
  })
  itemsPerPage: number;

  /**
   * Tổng số trang
   */
  @ApiProperty({
    description: 'Tổng số trang',
    example: 10,
    type: Number,
  })
  totalPages: number;

  /**
   * Trang hiện tại
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    type: Number,
  })
  currentPage: number;

  /**
   * có bản ghi nào không (bất kể search filter)
   */
  @ApiProperty({
    description: 'có bản ghi nào không (bất kể search filter)',
    example: true,
    type: Boolean,
  })
  hasItems?: boolean;
}

/**
 * Lớp định nghĩa kết quả trả về cho dữ liệu phân trang
 * Sử dụng generic type T để định nghĩa kiểu dữ liệu của các item trong danh sách
 */
export class PaginatedResult<T> {
  /** Danh sách các item */
  @ApiProperty({
    description: 'Danh sách các item',
    type: 'array',
    isArray: true,
  })
  items: T[];

  /** Thông tin phân trang */
  @ApiProperty({
    description: 'Thông tin phân trang',
    type: PaginationMeta,
  })
  meta: PaginationMeta;
}
