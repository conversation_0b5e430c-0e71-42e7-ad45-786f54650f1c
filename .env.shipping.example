# ===================================
# SHIPPING CONFIGURATION
# ===================================

# GHN (Giao Hàng Nhan<PERSON>) Configuration
# Lấy từ: https://khachhang.giaohangnhanh.vn/
GHN_TOKEN=your_ghn_token_here
GHN_SHOP_ID=your_ghn_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
# Production URL: https://online-gateway.ghn.vn

# GHTK (Giao Hàng Tiết Kiệm) Configuration  
# Lấy từ: https://khachhang.giaohangtietkiem.vn/
GHTK_TOKEN=your_ghtk_token_here
GHTK_PARTNER_CODE=your_ghtk_partner_code_here
GHTK_BASE_URL=https://services.giaohangtietkiem.vn
# Test URL: https://services.ghtklab.com

# Shop Information (for shipping) - DEPRECATED
# Thông tin shop giờ được lưu trong database thông qua API user shop info
# Không cần cấu hình trong .env nữa
# Sử dụng API: POST /v1/user/shop-info để tạo/cập nhật thông tin shop

# Shipping Configuration
SHIPPING_TIMEOUT=30000
SHIPPING_DEFAULT_WEIGHT=100
SHIPPING_DEFAULT_DIMENSIONS_LENGTH=20
SHIPPING_DEFAULT_DIMENSIONS_WIDTH=15
SHIPPING_DEFAULT_DIMENSIONS_HEIGHT=10

# Webhook URLs (optional - for receiving status updates)
GHN_WEBHOOK_URL=https://yourdomain.com/webhooks/shipping/ghn
GHTK_WEBHOOK_URL=https://yourdomain.com/webhooks/shipping/ghtk

# ===================================
# NOTES
# ===================================

# 1. GHN Token và Shop ID:
#    - Đăng ký tài khoản tại https://khachhang.giaohangnhanh.vn/
#    - Vào phần "Cài đặt" -> "Token" để lấy token
#    - Vào phần "Cửa hàng" để lấy Shop ID

# 2. GHTK Token:
#    - Đăng ký tài khoản tại https://khachhang.giaohangtietkiem.vn/
#    - Vào phần "Cài đặt" -> "API" để lấy token
#    - Partner code có thể để trống nếu không có

# 3. Shop Information:
#    - Thông tin shop giờ được quản lý qua database
#    - Sử dụng API /v1/user/shop-info để tạo/cập nhật
#    - Mỗi user có thể có thông tin shop riêng
#    - Thông tin này sẽ được sử dụng làm địa chỉ gửi hàng

# 4. Test Environment:
#    - Sử dụng URL test và token test để phát triển
#    - Chuyển sang production khi deploy thực tế

# 5. Webhook:
#    - Webhook URL để nhận cập nhật trạng thái từ đơn vị vận chuyển
#    - Cần cấu hình trên dashboard của GHN/GHTK
#    - Endpoint sẽ tự động xử lý và cập nhật trạng thái đơn hàng

# ===================================
# API ENDPOINTS FOR SHOP INFO
# ===================================

# GET /v1/user/shop-info - Lấy thông tin shop
# POST /v1/user/shop-info - Tạo/cập nhật thông tin shop
# PUT /v1/user/shop-info - Cập nhật một phần thông tin shop
# DELETE /v1/user/shop-info - Xóa thông tin shop
# GET /v1/user/shop-info/exists - Kiểm tra shop info có tồn tại không
