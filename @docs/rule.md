# Quy tắc phát triển API cho RedAI Backend

## C<PERSON><PERSON> trúc phản hồi API

### Ph<PERSON>n hồi thành công

Tất cả các API trong hệ thống phải trả về kết quả theo cấu trúc chuẩn sử dụng `ApiResponseDto` từ `@src/common/response/api-response-dto.ts`.

```typescript
{
  "code": 200,
  "message": "Success",
  "result": {
    // Dữ liệu phản hồi tùy thuộc vào API
  }
}
```

Ví dụ cách sử dụng trong controller:

```typescript
import { ApiResponseDto } from '@/common/response';

@Controller('users')
export class UserController {
  @Get()
  async findAll(): Promise<ApiResponseDto<User[]>> {
    const users = await this.userService.findAll();
    return new ApiResponseDto(users, '<PERSON><PERSON><PERSON> danh sách người dùng thành công');
  }
}
```

Hoặc sử dụng các phương thức tĩnh (static factory methods):

```typescript
import { ApiResponseDto } from '@/common/response';

@Controller('users')
export class UserController {
  @Get()
  async findAll(): Promise<ApiResponseDto<User[]>> {
    const users = await this.userService.findAll();
    return ApiResponseDto.success(users, 'Lấy danh sách người dùng thành công');
  }

  @Post()
  async create(@Body() createUserDto: CreateUserDto): Promise<ApiResponseDto<User>> {
    const user = await this.userService.create(createUserDto);
    return ApiResponseDto.created(user, 'Tạo người dùng thành công');
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<ApiResponseDto<null>> {
    await this.userService.remove(id);
    return ApiResponseDto.deleted(null, 'Xóa người dùng thành công');
  }
}
```

### Tham số truy vấn cho API danh sách

Tất cả các API lấy danh sách phải sử dụng `QueryDto` từ `@src/common/dto/query.dto.ts` làm cơ sở cho các tham số truy vấn:

```typescript
/**
 * Enum định nghĩa hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

/**
 * DTO cho các tham số query phổ biến được sử dụng trong các controller
 */
export class QueryDto {
  @ApiProperty({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'keyword',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Trường cần sắp xếp',
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
```

Khi cần mở rộng thêm các tham số truy vấn, hãy extend từ `QueryDto`:

```typescript
import { QueryDto } from '@/common/dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

export class UserQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái người dùng',
    enum: UserStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;
}
```

Ví dụ cách sử dụng trong controller:

```typescript
import { QueryDto } from '@/common/dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';

@Controller('users')
export class UserController {
  @Get()
  async findAll(
    @Query() queryDto: UserQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<User>>> {
    const paginatedUsers = await this.userService.findAllPaginated(queryDto);
    return wrapResponse(paginatedUsers, 'Lấy danh sách người dùng thành công');
  }
}
```

### Phản hồi cho dữ liệu phân trang

Tất cả các API trả về dữ liệu phân trang phải sử dụng cấu trúc chuẩn từ `@src/common/response/api-response-dto.ts` và phương thức tĩnh `ApiResponseDto.paginated()`:

```typescript
{
  "code": 200,
  "message": "Lấy danh sách thành công",
  "result": {
    "items": [
      // Danh sách các item
    ],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

Các interface chuẩn cho phân trang:

```typescript
/**
 * Interface định nghĩa cấu trúc metadata của kết quả phân trang
 */
export interface PaginationMeta {
  /**
   * Tổng số bản ghi
   */
  totalItems: number;

  /**
   * Số lượng bản ghi trên trang hiện tại
   */
  itemCount: number;

  /**
   * Số lượng bản ghi trên mỗi trang
   */
  itemsPerPage: number;

  /**
   * Tổng số trang
   */
  totalPages: number;

  /**
   * Trang hiện tại
   */
  currentPage: number;
}

/**
 * Interface định nghĩa kết quả trả về cho dữ liệu phân trang
 */
export interface PaginatedResult<T> {
  /** Danh sách các item */
  items: T[];
  /** Thông tin phân trang */
  meta: PaginationMeta;
}
```

Ví dụ cách sử dụng trong controller:

```typescript
import { ApiResponseDto, PaginatedResult } from '@/common/response';

@Controller('users')
export class UserController {
  @Get()
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10
  ): Promise<ApiResponseDto<PaginatedResult<User>>> {
    const paginatedUsers = await this.userService.findAllPaginated(page, limit);
    return ApiResponseDto.paginated(paginatedUsers, 'Lấy danh sách người dùng thành công');
  }
}
```

Hoặc sử dụng constructor trực tiếp:

```typescript
import { ApiResponseDto, PaginatedResult } from '@/common/response';

@Controller('users')
export class UserController {
  @Get()
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10
  ): Promise<ApiResponseDto<PaginatedResult<User>>> {
    const paginatedUsers = await this.userService.findAllPaginated(page, limit);
    return new ApiResponseDto(paginatedUsers, 'Lấy danh sách người dùng thành công');
  }
}
```

### Tổ chức mã lỗi theo module

Mỗi module trong hệ thống phải có thư mục `errors` riêng và định nghĩa các mã lỗi cụ thể cho module đó. Điều này giúp tổ chức mã lỗi một cách có cấu trúc và dễ quản lý.

#### 1. Cấu trúc thư mục

```
src/
└── modules/
    └── module-name/
        ├── errors/
        │   ├── index.ts                    # Export tất cả mã lỗi
        │   └── module-name-error.code.ts   # Định nghĩa mã lỗi cho module
        ├── module-name.controller.ts
        ├── module-name.service.ts
        └── ...
```

#### 2. Định nghĩa mã lỗi cho module

Mỗi module phải định nghĩa các mã lỗi riêng với phạm vi số cụ thể để tránh xung đột. Ví dụ:

- User module: 10000-10099
- Auth module: 11000-11099
- R-Point module: 12000-12099
- Marketing module: 13000-13099

Ví dụ về file định nghĩa mã lỗi cho module R-Point:

```typescript
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module R-Point (12000-12099)
 */
export const RPOINT_ERROR_CODES = {
  /**
   * Lỗi khi không tìm thấy gói point
   */
  POINT_PACKAGE_NOT_FOUND: new ErrorCode(
    12000,
    'Không tìm thấy gói point',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không tìm thấy mã giảm giá
   */
  COUPON_NOT_FOUND: new ErrorCode(
    12001,
    'Không tìm thấy mã giảm giá',
    HttpStatus.NOT_FOUND,
  ),

  // Các mã lỗi khác...
};
```

#### 3. Export mã lỗi

File `index.ts` trong thư mục `errors` nên export tất cả các mã lỗi:

```typescript
export * from './module-name-error.code';
```

### Phản hồi lỗi

Tất cả các lỗi trong hệ thống phải được xử lý bằng cách sử dụng `AppException` từ `@src/common/exceptions/app.exception.ts` với mã lỗi tương ứng từ module đó.

```typescript
import { AppException } from '@/common/exceptions';
import { RPOINT_ERROR_CODES } from './exceptions';

@Injectable()
export class RPointService {
  async findPointPackage(id: number): Promise<PointPackage> {
    const pointPackage = await this.pointPackageRepository.findOne({ where: { id } });

    if (!pointPackage) {
      throw new AppException(
        RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND,
        `Không tìm thấy gói point với ID ${id}`
      );
    }

    return pointPackage;
  }
}
```

Khi một `AppException` được ném ra, hệ thống sẽ tự động chuyển đổi nó thành phản hồi HTTP với cấu trúc chuẩn:

```json
{
  "code": 12000,
  "message": "Không tìm thấy gói point với ID 123",
  "result": null
}
```

## Danh sách mã lỗi

Các mã lỗi được định nghĩa trong `ErrorCode` và phải được sử dụng nhất quán trong toàn bộ ứng dụng. Dưới đây là một số mã lỗi thường dùng:

| Mã lỗi | Mô tả | HTTP Status |
|--------|-------|-------------|
| INTERNAL_SERVER_ERROR | Lỗi không xác định | 500 |
| DATABASE_ERROR | Lỗi Database | 500 |
| RESOURCE_NOT_FOUND | Resource not found | 404 |
| VALIDATION_ERROR | Lỗi validation | 400 |
| USER_NOT_FOUND | Không tìm thấy người dùng | 404 |
| EMAIL_ALREADY_EXISTS | Email đã được sử dụng | 400 |
| PHONE_NUMBER_ALREADY_EXISTS | Số điện thoại đã được sử dụng | 400 |
| TOKEN_INVALID_OR_EXPIRED | Token không hợp lệ hoặc đã hết hạn | 400 |
| UNAUTHORIZED_ACCESS | Không có quyền truy cập | 401 |

## Quy tắc xử lý lỗi

1. **Tổ chức mã lỗi theo module**:
   - Mỗi module phải có thư mục `errors` riêng và file định nghĩa mã lỗi.
   - Đặt tên constant theo format: `MODULE_NAME_ERROR_CODES`.
   - Sử dụng phạm vi số riêng cho mỗi module để tránh xung đột.
   - Viết comment đầy đủ cho mỗi mã lỗi để giải thích ý nghĩa.

2. **Không sử dụng các exception của NestJS trực tiếp**:
   - Không sử dụng `NotFoundException`, `BadRequestException`, `UnauthorizedException`, v.v.
   - Thay vào đó, sử dụng `AppException` với mã lỗi phù hợp từ module tương ứng.

3. **Xử lý lỗi trong try/catch**:
   ```typescript
   try {
     // Thực hiện một thao tác có thể gây lỗi
     await this.someService.doSomething();
   } catch (error) {
     // Nếu đã là AppException thì không cần wrap lại
     if (error instanceof AppException) {
       throw error;
     }

     // Wrap lỗi trong AppException với mã lỗi từ module
     throw new AppException(
       MODULE_ERROR_CODES.OPERATION_FAILED,
       'Không thể thực hiện thao tác',
       { originalError: error.message }
     );
   }
   ```

4. **Thêm mã lỗi mới khi cần thiết**:
   - Thêm mã lỗi mới vào file error code của module tương ứng.
   - Đảm bảo mã lỗi mới có mã số duy nhất trong phạm vi của module.
   - Viết comment đầy đủ để mô tả lỗi.
   - Cập nhật file index.ts để export mã lỗi mới.

5. **Đặt tên mã lỗi có ý nghĩa**:
   - Sử dụng tên mô tả rõ ràng về lỗi: `RESOURCE_NOT_FOUND`, `VALIDATION_ERROR`, v.v.
   - Sử dụng chữ hoa và gạch dưới để phân tách các từ: `USER_NOT_FOUND`.
   - Tránh sử dụng tên quá chung chung hoặc không rõ ràng.

## Authentication và Authorization

### API dành cho User

Tất cả các API dành cho user phải được bảo vệ bằng `JwtUserGuard` và được đánh dấu trong Swagger:

```typescript
@Controller('users')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserController {
  // Controller methods...
}
```

Để truy cập thông tin của user đã đăng nhập, sử dụng decorator `@CurrentUser`:

```typescript
@Get('profile')
async getProfile(@CurrentUser() user: JWTPayload): Promise<ApiResponseDto<UserProfileDto>> {
  const profile = await this.userService.getProfile(user.id);
  return ApiResponseDto.success(profile, 'Lấy thông tin profile thành công');
}
```

### API dành cho Admin/Employee

Tất cả các API dành cho admin/employee phải được bảo vệ bằng `JwtEmployeeGuard` và được đánh dấu trong Swagger:

```typescript
@Controller('admin/users')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth("JWT-auth")
export class AdminUserController {
  // Controller methods...
}
```

Để truy cập thông tin của employee đã đăng nhập, sử dụng decorator `@CurrentEmployee`:

```typescript
@Get('profile')
async getProfile(@CurrentEmployee() employee: JWTPayload): Promise<ApiResponseDto<EmployeeProfileDto>> {
  const profile = await this.employeeService.getProfile(employee.id);
  return ApiResponseDto.success(profile, 'Lấy thông tin profile thành công');
}
```

## Swagger Documentation

Tất cả các API phải được tài liệu hóa đầy đủ bằng Swagger để đảm bảo tính rõ ràng và dễ sử dụng. Dưới đây là các quy tắc cần tuân thủ:

### 1. Tài liệu ở cấp Controller

```typescript
@ApiTags('Users') // Nhóm API trong Swagger UI
@ApiBearerAuth('JWT-auth') // Xác định loại xác thực
@ApiExtraModels(UserDto, CreateUserDto, UpdateUserDto) // Đăng ký các model phụ
@Controller('users')
export class UserController {
  // Controller methods...
}
```

### 2. Tài liệu cho từng Endpoint

Mỗi endpoint phải có đầy đủ các decorator sau:

```typescript
// Mô tả tổng quan về endpoint
@ApiOperation({
  summary: 'Lấy thông tin người dùng',
  description: 'Trả về thông tin chi tiết của người dùng dựa trên ID'
})

// Mô tả các tham số
@ApiParam({
  name: 'id',
  description: 'ID của người dùng',
  type: 'number',
  example: 1
})

// Mô tả các query parameters (nếu có)
@ApiQuery({
  name: 'include',
  description: 'Các thông tin liên quan cần bao gồm',
  required: false,
  type: 'string',
  example: 'roles,permissions'
})

// Mô tả body request (nếu có)
@ApiBody({
  description: 'Thông tin người dùng cần cập nhật',
  type: UpdateUserDto
})

// Mô tả các response có thể có
@ApiResponse({
  status: 200,
  description: 'Lấy thông tin người dùng thành công',
  type: () => ApiResponseDto, // Sử dụng ApiResponseDto làm wrapper
  schema: {
    allOf: [
      { $ref: getSchemaPath(ApiResponseDto) },
      {
        properties: {
          result: { $ref: getSchemaPath(UserDto) }
        }
      }
    ]
  }
})
@ApiResponse({
  status: 404,
  description: 'Không tìm thấy người dùng',
})
@ApiResponse({
  status: 401,
  description: 'Không có quyền truy cập',
})

@Get(':id')
async findOne(@Param('id') id: number): Promise<ApiResponseDto<UserDto>> {
  const user = await this.userService.findOne(id);
  return ApiResponseDto.success(user, 'Lấy thông tin người dùng thành công');
}
```

### 3. Tài liệu cho DTO

Tất cả các DTO phải được tài liệu hóa đầy đủ:

```typescript
export class CreateUserDto {
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
    required: true
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Mật khẩu của người dùng',
    example: 'StrongPassword123',
    required: true,
    minLength: 8,
    maxLength: 20
  })
  @IsString()
  @MinLength(8)
  @MaxLength(20)
  password: string;

  @ApiProperty({
    description: 'Họ tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A',
    required: true
  })
  @IsString()
  fullName: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0912345678',
    required: false
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Trạng thái của người dùng',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    default: UserStatus.PENDING,
    required: false
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus = UserStatus.PENDING;
}
```

### 4. Tài liệu cho Phân trang

Đối với các API trả về dữ liệu phân trang, cần mô tả rõ cấu trúc phản hồi:

```typescript
@ApiOperation({ summary: 'Lấy danh sách người dùng' })
@ApiResponse({
  status: 200,
  description: 'Lấy danh sách người dùng thành công',
  schema: {
    allOf: [
      { $ref: getSchemaPath(ApiResponseDto) },
      {
        properties: {
          result: {
            properties: {
              items: {
                type: 'array',
                items: { $ref: getSchemaPath(UserDto) }
              },
              meta: {
                $ref: getSchemaPath(PaginationMeta)
              }
            }
          }
        }
      }
    ]
  }
})
@Get()
async findAll(
  @Query() queryDto: UserQueryDto
): Promise<ApiResponseDto<PaginatedResult<UserDto>>> {
  const paginatedUsers = await this.userService.findAllPaginated(queryDto);
  return ApiResponseDto.paginated(paginatedUsers, 'Lấy danh sách người dùng thành công');
}
```

### 5. Quy tắc chung

- **Nhất quán**: Sử dụng cùng một phong cách và cấu trúc tài liệu trong toàn bộ ứng dụng.
- **Đầy đủ**: Mô tả tất cả các tham số, body, và response có thể có.
- **Rõ ràng**: Sử dụng ngôn ngữ rõ ràng, dễ hiểu trong các mô tả.
- **Ví dụ**: Cung cấp ví dụ cho tất cả các tham số và response.
- **Cập nhật**: Đảm bảo tài liệu luôn được cập nhật khi API thay đổi.
- **Phân loại**: Sử dụng `@ApiTags` để phân loại API theo chức năng.
- **Xác thực**: Sử dụng `@ApiBearerAuth` để chỉ định yêu cầu xác thực.
- **Mô tả lỗi**: Mô tả tất cả các mã lỗi có thể xảy ra và ý nghĩa của chúng.

## Xử lý URL cho tài nguyên media (ảnh, file)

### Xử lý URL cho việc hiển thị tài nguyên

Tất cả các URL ảnh, file khi trả về cho frontend phải được xử lý thông qua `CdnService` từ `@src/shared/services/cdn.service.ts` để đảm bảo tính bảo mật và nhất quán.

#### 1. Sử dụng CdnService

```typescript
import { Injectable } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';

@Injectable()
export class UserService {
  constructor(private readonly cdnService: CdnService) {}

  async getUserProfile(userId: number): Promise<UserProfileDto> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    // Xử lý URL ảnh đại diện
    let avatarUrl = null;
    if (user.avatarKey) {
      // Tạo URL có chữ ký với thời hạn 1 giờ
      avatarUrl = this.cdnService.generateUrlView(
        user.avatarKey,
        TimeIntervalEnum.ONE_HOUR
      );
    }

    return {
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      avatarUrl: avatarUrl,
      // Các thông tin khác...
    };
  }
}
```

#### 2. Xử lý danh sách tài nguyên

Khi trả về danh sách các đối tượng có chứa URL tài nguyên, cần xử lý mỗi URL:

```typescript
async getArticles(queryDto: ArticleQueryDto): Promise<PaginatedResult<ArticleDto>> {
  const { items, meta } = await this.articleRepository.findWithPagination(queryDto);

  // Xử lý URL ảnh cho mỗi bài viết
  const processedItems = items.map(article => ({
    id: article.id,
    title: article.title,
    content: article.content,
    // Xử lý URL ảnh thumbnail
    thumbnailUrl: article.thumbnailKey
      ? this.cdnService.generateUrlView(
          article.thumbnailKey,
          TimeIntervalEnum.ONE_HOUR
        )
      : null,
    // Các thông tin khác...
  }));

  return {
    items: processedItems,
    meta
  };
}
```

#### 3. Thời hạn URL

Tùy thuộc vào loại tài nguyên và mục đích sử dụng, chọn thời hạn phù hợp từ `TimeIntervalEnum`:

```typescript
export enum TimeIntervalEnum {
  FIVE_MINUTES = 300,
  FIFTEEN_MINUTES = 900,
  THIRTY_MINUTES = 1800,
  ONE_HOUR = 3600,
  TWO_HOURS = 7200,
  FOUR_HOURS = 14400,
  EIGHT_HOURS = 28800,
  TWELVE_HOURS = 43200,
  ONE_DAY = 86400,
  TWO_DAYS = 172800,
  ONE_WEEK = 604800,
  ONE_MONTH = 2592000,
}
```

#### 4. Quy tắc chung

- **Không trả về đường dẫn trực tiếp**: Không bao giờ trả về đường dẫn tài nguyên trực tiếp từ storage mà không qua xử lý.
- **Không lưu URL đã xử lý vào database**: Chỉ lưu key của tài nguyên, không lưu URL đã được xử lý.
- **Xử lý null/undefined**: Luôn kiểm tra giá trị null/undefined trước khi xử lý URL.
- **Thời hạn phù hợp**: Chọn thời hạn URL phù hợp với mục đích sử dụng của tài nguyên.
- **Xử lý lỗi**: Bắt và xử lý lỗi khi gọi `generateUrlView` để tránh làm crash API.

### Xử lý URL cho việc upload tài nguyên

Khi cần cho phép frontend upload file, phải sử dụng `S3Service` từ `@src/shared/services/s3.service.ts` để tạo URL tạm thời có chữ ký số (presigned URL).

#### 1. Tạo URL tạm thời cho việc upload

```typescript
import { Injectable } from '@nestjs/common';
import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { MediaType } from '@utils/file';

@Injectable()
export class FileUploadService {
  constructor(private readonly s3Service: S3Service) {}

  /**
   * Tạo URL tạm thời để upload ảnh đại diện
   * @param userId ID của người dùng
   * @returns URL tạm thời có chữ ký số
   */
  async createAvatarUploadUrl(userId: number): Promise<string> {
    // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
    const key = `users/${userId}/avatar/${Date.now()}.jpg`;

    // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
    const presignedUrl = await this.s3Service.createPresignedWithID(
      key,
      TimeIntervalEnum.FIFTEEN_MINUTES,
      MediaType.IMAGE_JPEG,
      FileSizeEnum.ONE_MB
    );

    return presignedUrl;
  }
}
```

#### 2. Trả về URL tạm thời cho frontend

```typescript
import { Controller, Post, UseGuards } from '@nestjs/common';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response';
import { wrapResponse } from '@/common/helpers';
import { FileUploadService } from './file-upload.service';

@Controller('files')
@UseGuards(JwtUserGuard)
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('avatar/upload-url')
  async getAvatarUploadUrl(
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<{ uploadUrl: string; key: string }>> {
    // Tạo URL tạm thời
    const uploadUrl = await this.fileUploadService.createAvatarUploadUrl(user.id);

    // Trích xuất key từ URL (cần lưu lại để cập nhật vào database sau khi upload thành công)
    const key = uploadUrl.split('?')[0].split('/').slice(3).join('/');

    return ApiResponseDto.success(
      { uploadUrl, key },
      'Tạo URL upload ảnh đại diện thành công'
    );
  }
}
```

#### 3. Hướng dẫn frontend sử dụng URL tạm thời

Frontend cần thực hiện các bước sau để upload file:

1. Gọi API để lấy URL tạm thời
2. Sử dụng phương thức PUT để upload file trực tiếp lên URL tạm thời
3. Sau khi upload thành công, gọi API để cập nhật thông tin file trong database

```typescript
// Ví dụ code frontend (TypeScript)
async function uploadAvatar(file: File) {
  // 1. Lấy URL tạm thời từ API
  const response = await api.post('/files/avatar/upload-url');
  const { uploadUrl, key } = response.data.data;

  // 2. Upload file trực tiếp lên URL tạm thời
  await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type
    }
  });

  // 3. Cập nhật thông tin trong database
  await api.post('/users/profile/avatar', { avatarKey: key });

  // 4. Hiển thị thông báo thành công
  showSuccess('Upload ảnh đại diện thành công');
}
```

#### 4. Quy tắc chung

- **Sử dụng URL tạm thời**: Không bao giờ cho phép frontend upload trực tiếp lên storage mà không thông qua URL tạm thời có chữ ký số.
- **Giới hạn kích thước và loại file**: Luôn chỉ định rõ loại file và kích thước tối đa được phép upload.
- **Thời hạn phù hợp**: Chọn thời hạn URL tạm thời phù hợp (thường là 15 phút).
- **Xử lý lỗi**: Bắt và xử lý lỗi khi tạo URL tạm thời để tránh làm crash API.
- **Lưu key thay vì URL**: Chỉ lưu key của tài nguyên trong database, không lưu URL đầy đủ.



## Tuân thủ Entity và Database Schema

Khi làm việc với entity và database schema, cần tuân thủ các quy tắc sau:

1. **Không tự thêm trường vào entity**:
   - Không được tự ý thêm trường vào entity mà không có sự đồng thuận và review từ team.
   - Mọi thay đổi về cấu trúc entity phải được thông qua migration.
   - Đảm bảo các trường trong entity phản ánh chính xác cấu trúc của database.

2. **Sử dụng đúng kiểu dữ liệu**:
   - Kiểu dữ liệu trong entity phải tương thích với kiểu dữ liệu trong database.
   - Sử dụng các decorator TypeORM phù hợp để định nghĩa các ràng buộc và mối quan hệ.

3. **Không sử dụng relationship mapping khi không cần thiết**:
   - Không sử dụng các decorator như `@OneToOne`, `@OneToMany`, `@ManyToOne`, `@ManyToMany`.
   - Ưu tiên sử dụng các trường tham chiếu trực tiếp (như `userId` thay vì `@ManyToOne(() => User)`).

4. **Đảm bảo tính nhất quán**:
   - Các entity liên quan đến cùng một tính năng phải có cấu trúc nhất quán.
   - Tuân thủ quy ước đặt tên cho các trường và entity.

5. **Xử lý null và undefined**:
   - Đảm bảo các trường có thể null được đánh dấu rõ ràng với `nullable: true`.
   - Xử lý các trường có thể null một cách phù hợp trong code.

## Tuân thủ TypeScript

Tất cả mã nguồn phải tuân thủ nghiêm ngặt các quy tắc TypeScript và không được để lỗi TypeScript tồn tại trong codebase:

1. **Định nghĩa kiểu dữ liệu rõ ràng**:
   - Luôn khai báo kiểu dữ liệu cho các tham số hàm, biến và giá trị trả về.
   - Sử dụng interface hoặc type để định nghĩa cấu trúc dữ liệu.
   - Tránh sử dụng kiểu `any` khi có thể.

2. **Xử lý null và undefined**:
   - Sử dụng optional chaining (`?.`) và nullish coalescing (`??`) khi làm việc với giá trị có thể null/undefined.
   - Kiểm tra giá trị trước khi sử dụng để tránh lỗi runtime.

3. **Sử dụng strict mode**:
   - Dự án phải được cấu hình với `"strict": true` trong tsconfig.json.
   - Đảm bảo không có lỗi strict type checking.

4. **Không bỏ qua lỗi TypeScript**:
   - Không sử dụng `@ts-ignore` hoặc `@ts-nocheck` để bỏ qua lỗi TypeScript.
   - Nếu có vấn đề với thư viện bên thứ ba, sử dụng type declaration files hoặc tạo custom type definitions.

5. **Kiểm tra lỗi TypeScript trước khi commit**:
   - Chạy `npm run build` hoặc `npm run check-types` để kiểm tra lỗi TypeScript trước khi commit code.
   - CI pipeline phải bao gồm bước kiểm tra lỗi TypeScript.
2. **Không tự tạo entity mới**:
   - Không được tự ý tạo entity mới mà không có sự đồng thuận từ team.
   - Việc tạo entity mới phải đi kèm với migration tương ứng.
   - Cần tham khảo các file SQL để hiểu rõ cấu trúc database trước khi tạo entity.
## Kiểm tra và tuân thủ

- Tất cả các PR phải được kiểm tra để đảm bảo tuân thủ các quy tắc trên.
- Code review phải đảm bảo rằng các phản hồi API và xử lý lỗi đều tuân theo chuẩn đã định.
- Các test case phải bao gồm kiểm tra cấu trúc phản hồi API và xử lý lỗi.
- Không được merge code có lỗi TypeScript vào các nhánh chính.

## Quy trình phát triển và quản lý phiên bản

Khi phát triển tính năng mới, cần tuân thủ quy trình sau để đảm bảo chất lượng code và quản lý phiên bản hiệu quả:

1. **Lập kế hoạch và tài liệu hóa**:
   - Trước khi bắt đầu code một module mới, phải tạo một file kế hoạch định dạng markdown trong thư mục `@docs/plan`.
   - File kế hoạch phải bao gồm: mục tiêu, phạm vi, thiết kế API, cấu trúc dữ liệu, và các bước thực hiện.
   - Cập nhật tiến độ thực hiện vào file kế hoạch khi phát triển để theo dõi và báo cáo.
   - Đặt tên file theo format: `YYYYMMDD-module-name-plan.md` (ví dụ: `20230501-user-authentication-plan.md`).

2. **Tuân thủ chuẩn TypeScript và ESLint**:
   - Code phải tuân thủ nghiêm ngặt các quy tắc TypeScript và ESLint đã được cấu hình trong dự án.
   - Sử dụng lệnh `npm run lint` để kiểm tra lỗi ESLint trước khi commit.
   - Sử dụng lệnh `npm run build` hoặc `npm run check-types` để kiểm tra lỗi TypeScript.
   - Không được bỏ qua các cảnh báo từ ESLint hoặc TypeScript.

3. **Kiểm tra từng phần nhỏ**:
   - Sau khi hoàn thành mỗi phần chức năng nhỏ, hãy kiểm tra kỹ lưỡng trước khi tiếp tục.
   - Đảm bảo code đáp ứng tất cả các quy tắc đã đề ra trong tài liệu này.
   - Kiểm tra tính năng bằng cách chạy thử và xác nhận rằng nó hoạt động đúng như mong đợi.
   - Kiểm tra các trường hợp ngoại lệ và xử lý lỗi.
   - Cập nhật tiến độ vào file kế hoạch, đánh dấu các phần đã hoàn thành.

4. **Commit thường xuyên và có ý nghĩa**:
   - Khi một phần chức năng nhỏ đã hoàn thành và đáp ứng tất cả các quy tắc, hãy commit ngay:
     ```bash
     git add .
     git commit -m "Tên chức năng: Mô tả chi tiết những gì đã làm"
     ```
   - Đặt tên commit rõ ràng, mô tả chính xác những gì đã thay đổi.
   - Chia nhỏ commit thay vì gộp nhiều thay đổi lớn vào một commit.
   - Mỗi commit nên đại diện cho một đơn vị công việc hoàn chỉnh và có thể hoạt động độc lập.

5. **Quy tắc đặt tên commit**:
   - Bắt đầu bằng tên chức năng hoặc module: "User API: Thêm endpoint đăng ký"
   - Sử dụng thì hiện tại: "Thêm" thay vì "Đã thêm"
   - Mô tả ngắn gọn nhưng đầy đủ về những thay đổi
   - Nếu liên quan đến ticket/issue, thêm mã ticket: "[REDAI-123] User API: Thêm endpoint đăng ký"

Tuân thủ quy trình này sẽ giúp duy trì chất lượng code cao, giảm thiểu lỗi, và tạo điều kiện thuận lợi cho việc quản lý phiên bản và hợp tác trong team.

## Viết và chạy Unit Test

Việc viết unit test là một phần không thể thiếu trong quy trình phát triển để đảm bảo chất lượng code và giảm thiểu lỗi khi triển khai:

1. **Bắt buộc viết test cho mỗi tính năng**:
   - Sau khi code không còn lỗi nào (đã pass lint và type check), phải tiến hành viết test.
   - Mỗi tính năng mới phải có các test case tương ứng để kiểm tra tính đúng đắn.
   - Không được bỏ qua việc viết test với bất kỳ lý do gì.

2. **Độ bao phủ test (Test Coverage)**:
   - Cố gắng đạt độ bao phủ test tối thiểu 80% cho code mới.
   - Tập trung vào việc test các luồng xử lý chính và các trường hợp ngoại lệ.
   - Sử dụng lệnh `npm run test:cov` để kiểm tra độ bao phủ test.

3. **Cấu trúc test**:
   - Tổ chức test theo cấu trúc của code, mỗi file test tương ứng với một file code.
   - Sử dụng describe/it để mô tả rõ ràng mục đích của mỗi test case.
   - Mỗi test case chỉ nên kiểm tra một khía cạnh cụ thể của tính năng.

4. **Các loại test cần viết**:
   - **Unit Test**: Test từng hàm, phương thức riêng lẻ.
   - **Integration Test**: Test sự tương tác giữa các thành phần.
   - **E2E Test**: Test luồng hoạt động từ đầu đến cuối của tính năng.

5. **Mocking và Stubbing**:
   - Sử dụng mock/stub cho các dependency bên ngoài (database, API bên thứ ba, v.v.).
   - Đảm bảo test không phụ thuộc vào môi trường bên ngoài để tránh flaky test.
   - Sử dụng các công cụ như Jest, Sinon để tạo mock/stub.

6. **Chạy test trước khi commit**:
   - Chạy toàn bộ test suite trước khi commit để đảm bảo không có regression:
     ```bash
     npm run test
     ```
   - Nếu có test fail, phải sửa lỗi trước khi commit.

7. **Test cho API**:
   - Viết test cho tất cả các API endpoint.
   - Kiểm tra cả trường hợp thành công và thất bại.
   - Đảm bảo API trả về đúng cấu trúc response theo quy định.
   - Kiểm tra xử lý lỗi và validation.

8. **Continuous Integration**:
   - Cấu hình CI để chạy test tự động khi có pull request.
   - Không merge PR nếu test không pass.
   - Theo dõi và duy trì độ bao phủ test qua thời gian.

Việc viết test không chỉ giúp phát hiện lỗi sớm mà còn là tài liệu sống cho code, giúp các developer khác hiểu rõ hơn về cách hoạt động của tính năng và dễ dàng bảo trì, mở rộng trong tương lai.